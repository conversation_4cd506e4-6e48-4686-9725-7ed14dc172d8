{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/snack-bar/testing/snack-bar-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ContentContainerComponentHarness, HarnessPredicate, parallel} from '@angular/cdk/testing';\nimport {AriaLivePoliteness} from '@angular/cdk/a11y';\nimport {SnackBarHarnessFilters} from './snack-bar-harness-filters';\n\n/** <PERSON><PERSON><PERSON> for interacting with an MDC-based mat-snack-bar in tests. */\nexport class MatSnackBarHarness extends ContentContainerComponentHarness<string> {\n  // Developers can provide a custom component or template for the\n  // snackbar. The canonical snack-bar parent is the \"MatSnackBarContainer\".\n  /** The selector for the host element of a `MatSnackBar` instance. */\n  static hostSelector = '.mat-mdc-snack-bar-container:not([mat-exit])';\n  private _messageSelector = '.mdc-snackbar__label';\n  private _actionButtonSelector = '.mat-mdc-snack-bar-action';\n\n  private _snackBarLiveRegion = this.locatorFor('[aria-live]');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatSnackBarHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which snack bar instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: SnackBarHarnessFilters = {}): HarnessPredicate<MatSnackBarHarness> {\n    return new HarnessPredicate(MatSnackBarHarness, options);\n  }\n\n  /**\n   * Gets the role of the snack-bar. The role of a snack-bar is determined based\n   * on the ARIA politeness specified in the snack-bar config.\n   * @deprecated Use `getAriaLive` instead.\n   * @breaking-change 13.0.0\n   */\n  async getRole(): Promise<'alert' | 'status' | null> {\n    return (await this.host()).getAttribute('role') as Promise<'alert' | 'status' | null>;\n  }\n\n  /**\n   * Gets the aria-live of the snack-bar's live region. The aria-live of a snack-bar is\n   * determined based on the ARIA politeness specified in the snack-bar config.\n   */\n  async getAriaLive(): Promise<AriaLivePoliteness> {\n    return (await this._snackBarLiveRegion()).getAttribute(\n      'aria-live',\n    ) as Promise<AriaLivePoliteness>;\n  }\n\n  /**\n   * Whether the snack-bar has an action. Method cannot be used for snack-bar's with custom content.\n   */\n  async hasAction(): Promise<boolean> {\n    return (await this._getActionButton()) !== null;\n  }\n\n  /**\n   * Gets the description of the snack-bar. Method cannot be used for snack-bar's without action or\n   * with custom content.\n   */\n  async getActionDescription(): Promise<string> {\n    await this._assertHasAction();\n    return (await this._getActionButton())!.text();\n  }\n\n  /**\n   * Dismisses the snack-bar by clicking the action button. Method cannot be used for snack-bar's\n   * without action or with custom content.\n   */\n  async dismissWithAction(): Promise<void> {\n    await this._assertHasAction();\n    await (await this._getActionButton())!.click();\n  }\n\n  /**\n   * Gets the message of the snack-bar. Method cannot be used for snack-bar's with custom content.\n   */\n  async getMessage(): Promise<string> {\n    return (await this.locatorFor(this._messageSelector)()).text();\n  }\n\n  /** Gets whether the snack-bar has been dismissed. */\n  async isDismissed(): Promise<boolean> {\n    // We consider the snackbar dismissed if it's not in the DOM. We can assert that the\n    // element isn't in the DOM by seeing that its width and height are zero.\n\n    const host = await this.host();\n    const [exit, dimensions] = await parallel(() => [\n      // The snackbar container is marked with the \"exit\" attribute after it has been dismissed\n      // but before the animation has finished (after which it's removed from the DOM).\n      host.getAttribute('mat-exit'),\n      host.getDimensions(),\n    ]);\n\n    return exit != null || (!!dimensions && dimensions.height === 0 && dimensions.width === 0);\n  }\n\n  /**\n   * Asserts that the current snack-bar has an action defined. Otherwise the\n   * promise will reject.\n   */\n  private async _assertHasAction(): Promise<void> {\n    if (!(await this.hasAction())) {\n      throw Error('Method cannot be used for a snack-bar without an action.');\n    }\n  }\n\n  /** Gets the simple snack bar action button. */\n  private async _getActionButton() {\n    return this.locatorForOptional(this._actionButtonSelector)();\n  }\n}\n"], "names": [], "mappings": ";;AAYA;AACM,MAAO,kBAAmB,SAAQ,gCAAwC,CAAA;AAAhF,IAAA,WAAA,GAAA;;QAKU,IAAgB,CAAA,gBAAA,GAAG,sBAAsB,CAAC;QAC1C,IAAqB,CAAA,qBAAA,GAAG,2BAA2B,CAAC;AAEpD,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KA8F9D;;;;aAlGQ,IAAY,CAAA,YAAA,GAAG,8CAAH,CAAkD,EAAA;AAMrE;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAkC,EAAE,EAAA;AAC9C,QAAA,OAAO,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;KAC1D;AAED;;;;;AAKG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,MAAM,CAAuC,CAAC;KACvF;AAED;;;AAGG;AACH,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,mBAAmB,EAAE,EAAE,YAAY,CACpD,WAAW,CACmB,CAAC;KAClC;AAED;;AAEG;AACH,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,MAAM,IAAI,CAAC;KACjD;AAED;;;AAGG;AACH,IAAA,MAAM,oBAAoB,GAAA;AACxB,QAAA,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,EAAG,IAAI,EAAE,CAAC;KAChD;AAED;;;AAGG;AACH,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,MAAM,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,EAAG,KAAK,EAAE,CAAC;KAChD;AAED;;AAEG;AACH,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;KAChE;;AAGD,IAAA,MAAM,WAAW,GAAA;;;AAIf,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;;;AAG9C,YAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YAC7B,IAAI,CAAC,aAAa,EAAE;AACrB,SAAA,CAAC,CAAC;QAEH,OAAO,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;KAC5F;AAED;;;AAGG;AACK,IAAA,MAAM,gBAAgB,GAAA;QAC5B,IAAI,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;AAC7B,YAAA,MAAM,KAAK,CAAC,0DAA0D,CAAC,CAAC;SACzE;KACF;;AAGO,IAAA,MAAM,gBAAgB,GAAA;QAC5B,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC;KAC9D;;;;;"}