{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { importProvidersFrom } from '@angular/core';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nconst routes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./app/features/dashboard/dashboard.component').then(m => m.DashboardComponent)\n}, {\n  path: 'login',\n  loadComponent: () => import('./app/features/auth/login/login.component').then(m => m.LoginComponent)\n}];\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideAnimations(), provideHttpClient(withInterceptorsFromDi()), importProvidersFrom(MatSnackBarModule)]\n}).catch(err => console.error(err));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}