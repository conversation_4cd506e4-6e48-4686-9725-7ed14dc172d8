{"version": 3, "names": ["CommonActions", "DrawerActions", "useLinkBuilder", "React", "DrawerItem", "DrawerItemList", "state", "navigation", "descriptors", "buildLink", "focusedRoute", "routes", "index", "focusedDescriptor", "key", "focusedOptions", "options", "drawerActiveTintColor", "drawerInactiveTintColor", "drawerActiveBackgroundColor", "drawerInactiveBackgroundColor", "map", "route", "i", "focused", "onPress", "event", "emit", "type", "target", "canPreventDefault", "defaultPrevented", "dispatch", "closeDrawer", "navigate", "name", "merge", "title", "drawer<PERSON>abel", "drawerIcon", "drawerLabelStyle", "drawerItemStyle", "drawerAllowFontScaling", "undefined", "params"], "sourceRoot": "../../../src", "sources": ["views/DrawerItemList.tsx"], "mappings": "AAAA,SACEA,aAAa,EACbC,aAAa,EAGbC,cAAc,QACT,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAG9B,OAAOC,UAAU,MAAM,cAAc;AAQrC;AACA;AACA;AACA,eAAe,SAASC,cAAc,OAI5B;EAAA,IAJ6B;IACrCC,KAAK;IACLC,UAAU;IACVC;EACK,CAAC;EACN,MAAMC,SAAS,GAAGP,cAAc,EAAE;EAElC,MAAMQ,YAAY,GAAGJ,KAAK,CAACK,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC;EAC9C,MAAMC,iBAAiB,GAAGL,WAAW,CAACE,YAAY,CAACI,GAAG,CAAC;EACvD,MAAMC,cAAc,GAAGF,iBAAiB,CAACG,OAAO;EAEhD,MAAM;IACJC,qBAAqB;IACrBC,uBAAuB;IACvBC,2BAA2B;IAC3BC;EACF,CAAC,GAAGL,cAAc;EAElB,OAAOT,KAAK,CAACK,MAAM,CAACU,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;IACpC,MAAMC,OAAO,GAAGD,CAAC,KAAKjB,KAAK,CAACM,KAAK;IAEjC,MAAMa,OAAO,GAAG,MAAM;MACpB,MAAMC,KAAK,GAAGnB,UAAU,CAACoB,IAAI,CAAC;QAC5BC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAEP,KAAK,CAACR,GAAG;QACjBgB,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAI,CAACJ,KAAK,CAACK,gBAAgB,EAAE;QAC3BxB,UAAU,CAACyB,QAAQ,CAAC;UAClB,IAAIR,OAAO,GACPvB,aAAa,CAACgC,WAAW,EAAE,GAC3BjC,aAAa,CAACkC,QAAQ,CAAC;YAAEC,IAAI,EAAEb,KAAK,CAACa,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAC,CAAC,CAAC;UAC9DP,MAAM,EAAEvB,KAAK,CAACQ;QAChB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,MAAM;MACJuB,KAAK;MACLC,WAAW;MACXC,UAAU;MACVC,gBAAgB;MAChBC,eAAe;MACfC;IACF,CAAC,GAAGlC,WAAW,CAACc,KAAK,CAACR,GAAG,CAAC,CAACE,OAAO;IAElC,oBACE,oBAAC,UAAU;MACT,GAAG,EAAEM,KAAK,CAACR,GAAI;MACf,KAAK,EACHwB,WAAW,KAAKK,SAAS,GACrBL,WAAW,GACXD,KAAK,KAAKM,SAAS,GACnBN,KAAK,GACLf,KAAK,CAACa,IACX;MACD,IAAI,EAAEI,UAAW;MACjB,OAAO,EAAEf,OAAQ;MACjB,eAAe,EAAEP,qBAAsB;MACvC,iBAAiB,EAAEC,uBAAwB;MAC3C,qBAAqB,EAAEC,2BAA4B;MACnD,uBAAuB,EAAEC,6BAA8B;MACvD,gBAAgB,EAAEsB,sBAAuB;MACzC,UAAU,EAAEF,gBAAiB;MAC7B,KAAK,EAAEC,eAAgB;MACvB,EAAE,EAAEhC,SAAS,CAACa,KAAK,CAACa,IAAI,EAAEb,KAAK,CAACsB,MAAM,CAAE;MACxC,OAAO,EAAEnB;IAAQ,EACjB;EAEN,CAAC,CAAC;AACJ"}