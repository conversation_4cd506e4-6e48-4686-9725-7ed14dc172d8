/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;

if (i === 1 && v === 0)
    return 1;
return 5;
}
    global.ng.common.locales['et'] = ["et",[["AM","PM"],u,u],u,[["P","E","T","K","N","R","L"],u,["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["P","E","T","K","N","R","L"]],u,[["J","V","M","A","<PERSON>","J","J","A","S","O","N","D"],["jaan","veebr","märts","apr","mai","juuni","juuli","aug","sept","okt","nov","dets"],["jaanuar","veebruar","märts","aprill","mai","juuni","juuli","august","september","oktoober","november","detsember"]],u,[["eKr","pKr"],u,["enne Kristust","pärast Kristust"]],1,[6,0],["dd.MM.yy","d. MMM y","d. MMMM y","EEEE, d. MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[","," ",";","%","+","−","×10^","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00 ¤","#E0"],"EUR","€","euro",{"AUD":["AU$","$"],"EEK":["kr"],"PHP":[u,"₱"],"THB":["฿"],"TWD":["NT$"]},"ltr", plural, [[["keskööl","keskpäeval","hommikul","pärastlõunal","õhtul","öösel"],u,u],[["kesköö","keskpäev","hommik","pärastlõuna","õhtu","öö"],u,u],["00:00","12:00",["05:00","12:00"],["12:00","18:00"],["18:00","23:00"],["23:00","05:00"]]]];
  })(globalThis);
    