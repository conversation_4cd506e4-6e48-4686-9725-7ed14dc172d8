import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Observable } from 'rxjs';

interface DashboardStats {
  users: {
    total: number;
    active: number;
    new_today: number;
  };
  products: {
    total: number;
    published: number;
    out_of_stock: number;
    low_stock: number;
  };
  orders: {
    total: number;
    pending: number;
    processing: number;
    delivered: number;
    total_revenue: number;
    orders_today: number;
    revenue_today: number;
  };
  categories: {
    total: number;
    active: number;
    featured: number;
  };
}

interface RecentActivity {
  id: number;
  description: string;
  subject_type: string;
  subject_id: number;
  causer: {
    id: number;
    name: string;
  };
  created_at: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  stats: DashboardStats | null = null;
  recentActivities: RecentActivity[] = [];
  loading = false;
  error: string | null = null;

  // Mock data for now
  mockStats: DashboardStats = {
    users: { total: 150, active: 120, new_today: 5 },
    products: { total: 500, published: 450, out_of_stock: 10, low_stock: 25 },
    orders: { total: 1200, pending: 15, processing: 8, delivered: 1177, total_revenue: 45000, orders_today: 12, revenue_today: 850 },
    categories: { total: 25, active: 22, featured: 8 }
  };

  constructor() {}

  ngOnInit(): void {
    // Load mock data for now
    this.stats = this.mockStats;
    this.recentActivities = [
      {
        id: 1,
        description: 'New user registered',
        subject_type: 'App\\Models\\User',
        subject_id: 123,
        causer: { id: 1, name: 'System' },
        created_at: new Date().toISOString()
      },
      {
        id: 2,
        description: 'Product updated',
        subject_type: 'App\\Models\\Product',
        subject_id: 456,
        causer: { id: 2, name: 'Admin' },
        created_at: new Date(Date.now() - 3600000).toISOString()
      }
    ];
  }



  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 18) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getActivityIcon(subjectType: string): string {
    const iconMap: { [key: string]: string } = {
      'App\\Models\\User': 'person',
      'App\\Models\\Product': 'inventory',
      'App\\Models\\Order': 'shopping_cart',
      'App\\Models\\Category': 'category',
      'App\\Models\\Coupon': 'local_offer',
      'App\\Models\\Media': 'image',
      'default': 'info'
    };

    return iconMap[subjectType] || iconMap['default'];
  }

  getActivityColor(subjectType: string): string {
    const colorMap: { [key: string]: string } = {
      'App\\Models\\User': 'primary',
      'App\\Models\\Product': 'accent',
      'App\\Models\\Order': 'warn',
      'App\\Models\\Category': 'primary',
      'App\\Models\\Coupon': 'accent',
      'App\\Models\\Media': 'primary',
      'default': ''
    };

    return colorMap[subjectType] || colorMap['default'];
  }

  refreshDashboard(): void {
    this.loadDashboardData();
  }

  navigateToModule(module: string): void {
    // Navigation logic will be implemented when routing is set up
    console.log(`Navigate to ${module}`);
  }
}
