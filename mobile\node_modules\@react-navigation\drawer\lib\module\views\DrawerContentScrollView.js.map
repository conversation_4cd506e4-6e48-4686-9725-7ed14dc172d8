{"version": 3, "names": ["React", "I18nManager", "ScrollView", "StyleSheet", "useSafeAreaInsets", "DrawerPositionContext", "DrawerContentScrollView", "ref", "contentContainerStyle", "style", "children", "rest", "drawerPosition", "useContext", "insets", "isRight", "getConstants", "isRTL", "paddingTop", "top", "paddingStart", "left", "paddingEnd", "right", "styles", "container", "forwardRef", "create", "flex"], "sourceRoot": "../../../src", "sources": ["views/DrawerContentScrollView.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,WAAW,EACXC,UAAU,EAEVC,UAAU,QACL,cAAc;AACrB,SAASC,iBAAiB,QAAQ,gCAAgC;AAElE,OAAOC,qBAAqB,MAAM,gCAAgC;AAMlE,SAASC,uBAAuB,OAE9BC,GAA2B,EAC3B;EAAA,IAFA;IAAEC,qBAAqB;IAAEC,KAAK;IAAEC,QAAQ;IAAE,GAAGC;EAAY,CAAC;EAG1D,MAAMC,cAAc,GAAGZ,KAAK,CAACa,UAAU,CAACR,qBAAqB,CAAC;EAC9D,MAAMS,MAAM,GAAGV,iBAAiB,EAAE;EAElC,MAAMW,OAAO,GAAGd,WAAW,CAACe,YAAY,EAAE,CAACC,KAAK,GAC5CL,cAAc,KAAK,MAAM,GACzBA,cAAc,KAAK,OAAO;EAE9B,oBACE,oBAAC,UAAU,eACLD,IAAI;IACR,GAAG,EAAEJ,GAAI;IACT,qBAAqB,EAAE,CACrB;MACEW,UAAU,EAAEJ,MAAM,CAACK,GAAG,GAAG,CAAC;MAC1BC,YAAY,EAAE,CAACL,OAAO,GAAGD,MAAM,CAACO,IAAI,GAAG,CAAC;MACxCC,UAAU,EAAEP,OAAO,GAAGD,MAAM,CAACS,KAAK,GAAG;IACvC,CAAC,EACDf,qBAAqB,CACrB;IACF,KAAK,EAAE,CAACgB,MAAM,CAACC,SAAS,EAAEhB,KAAK;EAAE,IAEhCC,QAAQ,CACE;AAEjB;AAEA,4BAAeV,KAAK,CAAC0B,UAAU,CAACpB,uBAAuB,CAAC;AAExD,MAAMkB,MAAM,GAAGrB,UAAU,CAACwB,MAAM,CAAC;EAC/BF,SAAS,EAAE;IACTG,IAAI,EAAE;EACR;AACF,CAAC,CAAC"}