"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.green800 = exports.green700 = exports.green600 = exports.green500 = exports.green50 = exports.green400 = exports.green300 = exports.green200 = exports.green100 = exports.deepPurpleA700 = exports.deepPurpleA400 = exports.deepPurpleA200 = exports.deepPurpleA100 = exports.deepPurple900 = exports.deepPurple800 = exports.deepPurple700 = exports.deepPurple600 = exports.deepPurple500 = exports.deepPurple50 = exports.deepPurple400 = exports.deepPurple300 = exports.deepPurple200 = exports.deepPurple100 = exports.deepOrangeA700 = exports.deepOrangeA400 = exports.deepOrangeA200 = exports.deepOrangeA100 = exports.deepOrange900 = exports.deepOrange800 = exports.deepOrange700 = exports.deepOrange600 = exports.deepOrange500 = exports.deepOrange50 = exports.deepOrange400 = exports.deepOrange300 = exports.deepOrange200 = exports.deepOrange100 = exports.cyanA700 = exports.cyanA400 = exports.cyanA200 = exports.cyanA100 = exports.cyan900 = exports.cyan800 = exports.cyan700 = exports.cyan600 = exports.cyan500 = exports.cyan50 = exports.cyan400 = exports.cyan300 = exports.cyan200 = exports.cyan100 = exports.brown900 = exports.brown800 = exports.brown700 = exports.brown600 = exports.brown500 = exports.brown50 = exports.brown400 = exports.brown300 = exports.brown200 = exports.brown100 = exports.blueGrey900 = exports.blueGrey800 = exports.blueGrey700 = exports.blueGrey600 = exports.blueGrey500 = exports.blueGrey50 = exports.blueGrey400 = exports.blueGrey300 = exports.blueGrey200 = exports.blueGrey100 = exports.blueA700 = exports.blueA400 = exports.blueA200 = exports.blueA100 = exports.blue900 = exports.blue800 = exports.blue700 = exports.blue600 = exports.blue500 = exports.blue50 = exports.blue400 = exports.blue300 = exports.blue200 = exports.blue100 = exports.black = exports.amberA700 = exports.amberA400 = exports.amberA200 = exports.amberA100 = exports.amber900 = exports.amber800 = exports.amber700 = exports.amber600 = exports.amber500 = exports.amber50 = exports.amber400 = exports.amber300 = exports.amber200 = exports.amber100 = void 0;
exports.purple100 = exports.pinkA700 = exports.pinkA400 = exports.pinkA200 = exports.pinkA100 = exports.pink900 = exports.pink800 = exports.pink700 = exports.pink600 = exports.pink500 = exports.pink50 = exports.pink400 = exports.pink300 = exports.pink200 = exports.pink100 = exports.orangeA700 = exports.orangeA400 = exports.orangeA200 = exports.orangeA100 = exports.orange900 = exports.orange800 = exports.orange700 = exports.orange600 = exports.orange500 = exports.orange50 = exports.orange400 = exports.orange300 = exports.orange200 = exports.orange100 = exports.limeA700 = exports.limeA400 = exports.limeA200 = exports.limeA100 = exports.lime900 = exports.lime800 = exports.lime700 = exports.lime600 = exports.lime500 = exports.lime50 = exports.lime400 = exports.lime300 = exports.lime200 = exports.lime100 = exports.lightGreenA700 = exports.lightGreenA400 = exports.lightGreenA200 = exports.lightGreenA100 = exports.lightGreen900 = exports.lightGreen800 = exports.lightGreen700 = exports.lightGreen600 = exports.lightGreen500 = exports.lightGreen50 = exports.lightGreen400 = exports.lightGreen300 = exports.lightGreen200 = exports.lightGreen100 = exports.lightBlueA700 = exports.lightBlueA400 = exports.lightBlueA200 = exports.lightBlueA100 = exports.lightBlue900 = exports.lightBlue800 = exports.lightBlue700 = exports.lightBlue600 = exports.lightBlue500 = exports.lightBlue50 = exports.lightBlue400 = exports.lightBlue300 = exports.lightBlue200 = exports.lightBlue100 = exports.indigoA700 = exports.indigoA400 = exports.indigoA200 = exports.indigoA100 = exports.indigo900 = exports.indigo800 = exports.indigo700 = exports.indigo600 = exports.indigo500 = exports.indigo50 = exports.indigo400 = exports.indigo300 = exports.indigo200 = exports.indigo100 = exports.grey900 = exports.grey800 = exports.grey700 = exports.grey600 = exports.grey500 = exports.grey50 = exports.grey400 = exports.grey300 = exports.grey200 = exports.grey100 = exports.greenA700 = exports.greenA400 = exports.greenA200 = exports.greenA100 = exports.green900 = void 0;
exports.yellowA700 = exports.yellowA400 = exports.yellowA200 = exports.yellowA100 = exports.yellow900 = exports.yellow800 = exports.yellow700 = exports.yellow600 = exports.yellow500 = exports.yellow50 = exports.yellow400 = exports.yellow300 = exports.yellow200 = exports.yellow100 = exports.white = exports.transparent = exports.tealA700 = exports.tealA400 = exports.tealA200 = exports.tealA100 = exports.teal900 = exports.teal800 = exports.teal700 = exports.teal600 = exports.teal500 = exports.teal50 = exports.teal400 = exports.teal300 = exports.teal200 = exports.teal100 = exports.redA700 = exports.redA400 = exports.redA200 = exports.redA100 = exports.red900 = exports.red800 = exports.red700 = exports.red600 = exports.red500 = exports.red50 = exports.red400 = exports.red300 = exports.red200 = exports.red100 = exports.purpleA700 = exports.purpleA400 = exports.purpleA200 = exports.purpleA100 = exports.purple900 = exports.purple800 = exports.purple700 = exports.purple600 = exports.purple500 = exports.purple50 = exports.purple400 = exports.purple300 = exports.purple200 = void 0;
const transparent = exports.transparent = 'rgba(255, 255, 255, 0)';
const red50 = exports.red50 = '#ffebee';
const red100 = exports.red100 = '#ffcdd2';
const red200 = exports.red200 = '#ef9a9a';
const red300 = exports.red300 = '#e57373';
const red400 = exports.red400 = '#ef5350';
const red500 = exports.red500 = '#f44336';
const red600 = exports.red600 = '#e53935';
const red700 = exports.red700 = '#d32f2f';
const red800 = exports.red800 = '#c62828';
const red900 = exports.red900 = '#b71c1c';
const redA100 = exports.redA100 = '#ff8a80';
const redA200 = exports.redA200 = '#ff5252';
const redA400 = exports.redA400 = '#ff1744';
const redA700 = exports.redA700 = '#d50000';
const pink50 = exports.pink50 = '#fce4ec';
const pink100 = exports.pink100 = '#f8bbd0';
const pink200 = exports.pink200 = '#f48fb1';
const pink300 = exports.pink300 = '#f06292';
const pink400 = exports.pink400 = '#ec407a';
const pink500 = exports.pink500 = '#e91e63';
const pink600 = exports.pink600 = '#d81b60';
const pink700 = exports.pink700 = '#c2185b';
const pink800 = exports.pink800 = '#ad1457';
const pink900 = exports.pink900 = '#880e4f';
const pinkA100 = exports.pinkA100 = '#ff80ab';
const pinkA200 = exports.pinkA200 = '#ff4081';
const pinkA400 = exports.pinkA400 = '#f50057';
const pinkA700 = exports.pinkA700 = '#c51162';
const purple50 = exports.purple50 = '#f3e5f5';
const purple100 = exports.purple100 = '#e1bee7';
const purple200 = exports.purple200 = '#ce93d8';
const purple300 = exports.purple300 = '#ba68c8';
const purple400 = exports.purple400 = '#ab47bc';
const purple500 = exports.purple500 = '#9c27b0';
const purple600 = exports.purple600 = '#8e24aa';
const purple700 = exports.purple700 = '#7b1fa2';
const purple800 = exports.purple800 = '#6a1b9a';
const purple900 = exports.purple900 = '#4a148c';
const purpleA100 = exports.purpleA100 = '#ea80fc';
const purpleA200 = exports.purpleA200 = '#e040fb';
const purpleA400 = exports.purpleA400 = '#d500f9';
const purpleA700 = exports.purpleA700 = '#aa00ff';
const deepPurple50 = exports.deepPurple50 = '#ede7f6';
const deepPurple100 = exports.deepPurple100 = '#d1c4e9';
const deepPurple200 = exports.deepPurple200 = '#b39ddb';
const deepPurple300 = exports.deepPurple300 = '#9575cd';
const deepPurple400 = exports.deepPurple400 = '#7e57c2';
const deepPurple500 = exports.deepPurple500 = '#673ab7';
const deepPurple600 = exports.deepPurple600 = '#5e35b1';
const deepPurple700 = exports.deepPurple700 = '#512da8';
const deepPurple800 = exports.deepPurple800 = '#4527a0';
const deepPurple900 = exports.deepPurple900 = '#311b92';
const deepPurpleA100 = exports.deepPurpleA100 = '#b388ff';
const deepPurpleA200 = exports.deepPurpleA200 = '#7c4dff';
const deepPurpleA400 = exports.deepPurpleA400 = '#651fff';
const deepPurpleA700 = exports.deepPurpleA700 = '#6200ea';
const indigo50 = exports.indigo50 = '#e8eaf6';
const indigo100 = exports.indigo100 = '#c5cae9';
const indigo200 = exports.indigo200 = '#9fa8da';
const indigo300 = exports.indigo300 = '#7986cb';
const indigo400 = exports.indigo400 = '#5c6bc0';
const indigo500 = exports.indigo500 = '#3f51b5';
const indigo600 = exports.indigo600 = '#3949ab';
const indigo700 = exports.indigo700 = '#303f9f';
const indigo800 = exports.indigo800 = '#283593';
const indigo900 = exports.indigo900 = '#1a237e';
const indigoA100 = exports.indigoA100 = '#8c9eff';
const indigoA200 = exports.indigoA200 = '#536dfe';
const indigoA400 = exports.indigoA400 = '#3d5afe';
const indigoA700 = exports.indigoA700 = '#304ffe';
const blue50 = exports.blue50 = '#e3f2fd';
const blue100 = exports.blue100 = '#bbdefb';
const blue200 = exports.blue200 = '#90caf9';
const blue300 = exports.blue300 = '#64b5f6';
const blue400 = exports.blue400 = '#42a5f5';
const blue500 = exports.blue500 = '#2196f3';
const blue600 = exports.blue600 = '#1e88e5';
const blue700 = exports.blue700 = '#1976d2';
const blue800 = exports.blue800 = '#1565c0';
const blue900 = exports.blue900 = '#0d47a1';
const blueA100 = exports.blueA100 = '#82b1ff';
const blueA200 = exports.blueA200 = '#448aff';
const blueA400 = exports.blueA400 = '#2979ff';
const blueA700 = exports.blueA700 = '#2962ff';
const lightBlue50 = exports.lightBlue50 = '#e1f5fe';
const lightBlue100 = exports.lightBlue100 = '#b3e5fc';
const lightBlue200 = exports.lightBlue200 = '#81d4fa';
const lightBlue300 = exports.lightBlue300 = '#4fc3f7';
const lightBlue400 = exports.lightBlue400 = '#29b6f6';
const lightBlue500 = exports.lightBlue500 = '#03a9f4';
const lightBlue600 = exports.lightBlue600 = '#039be5';
const lightBlue700 = exports.lightBlue700 = '#0288d1';
const lightBlue800 = exports.lightBlue800 = '#0277bd';
const lightBlue900 = exports.lightBlue900 = '#01579b';
const lightBlueA100 = exports.lightBlueA100 = '#80d8ff';
const lightBlueA200 = exports.lightBlueA200 = '#40c4ff';
const lightBlueA400 = exports.lightBlueA400 = '#00b0ff';
const lightBlueA700 = exports.lightBlueA700 = '#0091ea';
const cyan50 = exports.cyan50 = '#e0f7fa';
const cyan100 = exports.cyan100 = '#b2ebf2';
const cyan200 = exports.cyan200 = '#80deea';
const cyan300 = exports.cyan300 = '#4dd0e1';
const cyan400 = exports.cyan400 = '#26c6da';
const cyan500 = exports.cyan500 = '#00bcd4';
const cyan600 = exports.cyan600 = '#00acc1';
const cyan700 = exports.cyan700 = '#0097a7';
const cyan800 = exports.cyan800 = '#00838f';
const cyan900 = exports.cyan900 = '#006064';
const cyanA100 = exports.cyanA100 = '#84ffff';
const cyanA200 = exports.cyanA200 = '#18ffff';
const cyanA400 = exports.cyanA400 = '#00e5ff';
const cyanA700 = exports.cyanA700 = '#00b8d4';
const teal50 = exports.teal50 = '#e0f2f1';
const teal100 = exports.teal100 = '#b2dfdb';
const teal200 = exports.teal200 = '#80cbc4';
const teal300 = exports.teal300 = '#4db6ac';
const teal400 = exports.teal400 = '#26a69a';
const teal500 = exports.teal500 = '#009688';
const teal600 = exports.teal600 = '#00897b';
const teal700 = exports.teal700 = '#00796b';
const teal800 = exports.teal800 = '#00695c';
const teal900 = exports.teal900 = '#004d40';
const tealA100 = exports.tealA100 = '#a7ffeb';
const tealA200 = exports.tealA200 = '#64ffda';
const tealA400 = exports.tealA400 = '#1de9b6';
const tealA700 = exports.tealA700 = '#00bfa5';
const green50 = exports.green50 = '#e8f5e9';
const green100 = exports.green100 = '#c8e6c9';
const green200 = exports.green200 = '#a5d6a7';
const green300 = exports.green300 = '#81c784';
const green400 = exports.green400 = '#66bb6a';
const green500 = exports.green500 = '#4caf50';
const green600 = exports.green600 = '#43a047';
const green700 = exports.green700 = '#388e3c';
const green800 = exports.green800 = '#2e7d32';
const green900 = exports.green900 = '#1b5e20';
const greenA100 = exports.greenA100 = '#b9f6ca';
const greenA200 = exports.greenA200 = '#69f0ae';
const greenA400 = exports.greenA400 = '#00e676';
const greenA700 = exports.greenA700 = '#00c853';
const lightGreen50 = exports.lightGreen50 = '#f1f8e9';
const lightGreen100 = exports.lightGreen100 = '#dcedc8';
const lightGreen200 = exports.lightGreen200 = '#c5e1a5';
const lightGreen300 = exports.lightGreen300 = '#aed581';
const lightGreen400 = exports.lightGreen400 = '#9ccc65';
const lightGreen500 = exports.lightGreen500 = '#8bc34a';
const lightGreen600 = exports.lightGreen600 = '#7cb342';
const lightGreen700 = exports.lightGreen700 = '#689f38';
const lightGreen800 = exports.lightGreen800 = '#558b2f';
const lightGreen900 = exports.lightGreen900 = '#33691e';
const lightGreenA100 = exports.lightGreenA100 = '#ccff90';
const lightGreenA200 = exports.lightGreenA200 = '#b2ff59';
const lightGreenA400 = exports.lightGreenA400 = '#76ff03';
const lightGreenA700 = exports.lightGreenA700 = '#64dd17';
const lime50 = exports.lime50 = '#f9fbe7';
const lime100 = exports.lime100 = '#f0f4c3';
const lime200 = exports.lime200 = '#e6ee9c';
const lime300 = exports.lime300 = '#dce775';
const lime400 = exports.lime400 = '#d4e157';
const lime500 = exports.lime500 = '#cddc39';
const lime600 = exports.lime600 = '#c0ca33';
const lime700 = exports.lime700 = '#afb42b';
const lime800 = exports.lime800 = '#9e9d24';
const lime900 = exports.lime900 = '#827717';
const limeA100 = exports.limeA100 = '#f4ff81';
const limeA200 = exports.limeA200 = '#eeff41';
const limeA400 = exports.limeA400 = '#c6ff00';
const limeA700 = exports.limeA700 = '#aeea00';
const yellow50 = exports.yellow50 = '#fffde7';
const yellow100 = exports.yellow100 = '#fff9c4';
const yellow200 = exports.yellow200 = '#fff59d';
const yellow300 = exports.yellow300 = '#fff176';
const yellow400 = exports.yellow400 = '#ffee58';
const yellow500 = exports.yellow500 = '#ffeb3b';
const yellow600 = exports.yellow600 = '#fdd835';
const yellow700 = exports.yellow700 = '#fbc02d';
const yellow800 = exports.yellow800 = '#f9a825';
const yellow900 = exports.yellow900 = '#f57f17';
const yellowA100 = exports.yellowA100 = '#ffff8d';
const yellowA200 = exports.yellowA200 = '#ffff00';
const yellowA400 = exports.yellowA400 = '#ffea00';
const yellowA700 = exports.yellowA700 = '#ffd600';
const amber50 = exports.amber50 = '#fff8e1';
const amber100 = exports.amber100 = '#ffecb3';
const amber200 = exports.amber200 = '#ffe082';
const amber300 = exports.amber300 = '#ffd54f';
const amber400 = exports.amber400 = '#ffca28';
const amber500 = exports.amber500 = '#ffc107';
const amber600 = exports.amber600 = '#ffb300';
const amber700 = exports.amber700 = '#ffa000';
const amber800 = exports.amber800 = '#ff8f00';
const amber900 = exports.amber900 = '#ff6f00';
const amberA100 = exports.amberA100 = '#ffe57f';
const amberA200 = exports.amberA200 = '#ffd740';
const amberA400 = exports.amberA400 = '#ffc400';
const amberA700 = exports.amberA700 = '#ffab00';
const orange50 = exports.orange50 = '#fff3e0';
const orange100 = exports.orange100 = '#ffe0b2';
const orange200 = exports.orange200 = '#ffcc80';
const orange300 = exports.orange300 = '#ffb74d';
const orange400 = exports.orange400 = '#ffa726';
const orange500 = exports.orange500 = '#ff9800';
const orange600 = exports.orange600 = '#fb8c00';
const orange700 = exports.orange700 = '#f57c00';
const orange800 = exports.orange800 = '#ef6c00';
const orange900 = exports.orange900 = '#e65100';
const orangeA100 = exports.orangeA100 = '#ffd180';
const orangeA200 = exports.orangeA200 = '#ffab40';
const orangeA400 = exports.orangeA400 = '#ff9100';
const orangeA700 = exports.orangeA700 = '#ff6d00';
const deepOrange50 = exports.deepOrange50 = '#fbe9e7';
const deepOrange100 = exports.deepOrange100 = '#ffccbc';
const deepOrange200 = exports.deepOrange200 = '#ffab91';
const deepOrange300 = exports.deepOrange300 = '#ff8a65';
const deepOrange400 = exports.deepOrange400 = '#ff7043';
const deepOrange500 = exports.deepOrange500 = '#ff5722';
const deepOrange600 = exports.deepOrange600 = '#f4511e';
const deepOrange700 = exports.deepOrange700 = '#e64a19';
const deepOrange800 = exports.deepOrange800 = '#d84315';
const deepOrange900 = exports.deepOrange900 = '#bf360c';
const deepOrangeA100 = exports.deepOrangeA100 = '#ff9e80';
const deepOrangeA200 = exports.deepOrangeA200 = '#ff6e40';
const deepOrangeA400 = exports.deepOrangeA400 = '#ff3d00';
const deepOrangeA700 = exports.deepOrangeA700 = '#dd2c00';
const brown50 = exports.brown50 = '#efebe9';
const brown100 = exports.brown100 = '#d7ccc8';
const brown200 = exports.brown200 = '#bcaaa4';
const brown300 = exports.brown300 = '#a1887f';
const brown400 = exports.brown400 = '#8d6e63';
const brown500 = exports.brown500 = '#795548';
const brown600 = exports.brown600 = '#6d4c41';
const brown700 = exports.brown700 = '#5d4037';
const brown800 = exports.brown800 = '#4e342e';
const brown900 = exports.brown900 = '#3e2723';
const blueGrey50 = exports.blueGrey50 = '#eceff1';
const blueGrey100 = exports.blueGrey100 = '#cfd8dc';
const blueGrey200 = exports.blueGrey200 = '#b0bec5';
const blueGrey300 = exports.blueGrey300 = '#90a4ae';
const blueGrey400 = exports.blueGrey400 = '#78909c';
const blueGrey500 = exports.blueGrey500 = '#607d8b';
const blueGrey600 = exports.blueGrey600 = '#546e7a';
const blueGrey700 = exports.blueGrey700 = '#455a64';
const blueGrey800 = exports.blueGrey800 = '#37474f';
const blueGrey900 = exports.blueGrey900 = '#263238';
const grey50 = exports.grey50 = '#fafafa';
const grey100 = exports.grey100 = '#f5f5f5';
const grey200 = exports.grey200 = '#eeeeee';
const grey300 = exports.grey300 = '#e0e0e0';
const grey400 = exports.grey400 = '#bdbdbd';
const grey500 = exports.grey500 = '#9e9e9e';
const grey600 = exports.grey600 = '#757575';
const grey700 = exports.grey700 = '#616161';
const grey800 = exports.grey800 = '#424242';
const grey900 = exports.grey900 = '#212121';
const black = exports.black = '#000000';
const white = exports.white = '#ffffff';
//# sourceMappingURL=colors.js.map