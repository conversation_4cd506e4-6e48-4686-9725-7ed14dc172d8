{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { filter } from './filter';\nimport { takeLast } from './takeLast';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { identity } from '../util/identity';\nexport function last(predicate, defaultValue) {\n  const hasDefaultValue = arguments.length >= 2;\n  return source => source.pipe(predicate ? filter((v, i) => predicate(v, i, source)) : identity, takeLast(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new EmptyError()));\n}\n//# sourceMappingURL=last.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}