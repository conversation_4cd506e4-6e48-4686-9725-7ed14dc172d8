{"ast": null, "code": "import { AsyncSubject } from '../AsyncSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishLast() {\n  return source => {\n    const subject = new AsyncSubject();\n    return new ConnectableObservable(source, () => subject);\n  };\n}\n//# sourceMappingURL=publishLast.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}