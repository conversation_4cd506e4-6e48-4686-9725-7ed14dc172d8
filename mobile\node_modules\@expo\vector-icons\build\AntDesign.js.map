{"version": 3, "file": "AntDesign.js", "sourceRoot": "", "sources": ["../src/AntDesign.ts"], "names": [], "mappings": "AAAA,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,wDAAwD,CAAC;AAC1E,OAAO,QAAQ,MAAM,6DAA6D,CAAC;AAEnF,eAAe,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/AntDesign.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/AntDesign.json';\n\nexport default createIconSet(glyphMap, 'anticon', font);\n"]}