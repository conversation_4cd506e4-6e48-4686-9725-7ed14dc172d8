{"version": 3, "file": "EasBuild.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_EasBuildGradleScript", "Paths", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "APPLY_EAS_GRADLE", "hasApplyLine", "content", "applyLine", "replace", "split", "some", "line", "getEasBuildGradlePath", "projectRoot", "path", "join", "configureEasBuildAsync", "buildGradlePath", "getAppBuildGradleFilePath", "easGradlePath", "fs", "promises", "writeFile", "gradleScript", "buildGradleContent", "readFile", "hasEasGradleApply", "trim", "isEasBuildGradleConfiguredAsync", "hasEasGradleFile", "existsSync"], "sources": ["../../src/android/EasBuild.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport gradleScript from './EasBuildGradleScript';\nimport * as Paths from './Paths';\n\nconst APPLY_EAS_GRADLE = 'apply from: \"./eas-build.gradle\"';\n\nfunction hasApplyLine(content: string, applyLine: string): boolean {\n  return (\n    content\n      .replace(/\\r\\n/g, '\\n')\n      .split('\\n')\n      // Check for both single and double quotes\n      .some((line) => line === applyLine || line === applyLine.replace(/\"/g, \"'\"))\n  );\n}\n\nexport function getEasBuildGradlePath(projectRoot: string): string {\n  return path.join(projectRoot, 'android', 'app', 'eas-build.gradle');\n}\n\nexport async function configureEasBuildAsync(projectRoot: string): Promise<void> {\n  const buildGradlePath = Paths.getAppBuildGradleFilePath(projectRoot);\n  const easGradlePath = getEasBuildGradlePath(projectRoot);\n\n  await fs.promises.writeFile(easGradlePath, gradleScript);\n\n  const buildGradleContent = await fs.promises.readFile(path.join(buildGradlePath), 'utf8');\n\n  const hasEasGradleApply = hasApplyLine(buildGradleContent, APPLY_EAS_GRADLE);\n\n  if (!hasEasGradleApply) {\n    await fs.promises.writeFile(\n      buildGradlePath,\n      `${buildGradleContent.trim()}\\n${APPLY_EAS_GRADLE}\\n`\n    );\n  }\n}\n\nexport async function isEasBuildGradleConfiguredAsync(projectRoot: string): Promise<boolean> {\n  const buildGradlePath = Paths.getAppBuildGradleFilePath(projectRoot);\n  const easGradlePath = getEasBuildGradlePath(projectRoot);\n\n  const hasEasGradleFile = await fs.existsSync(easGradlePath);\n\n  const buildGradleContent = await fs.promises.readFile(path.join(buildGradlePath), 'utf8');\n  const hasEasGradleApply = hasApplyLine(buildGradleContent, APPLY_EAS_GRADLE);\n\n  return hasEasGradleApply && hasEasGradleFile;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,sBAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,qBAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiC,SAAAO,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAAjB,uBAAAW,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjC,MAAMiB,gBAAgB,GAAG,kCAAkC;AAE3D,SAASC,YAAYA,CAACC,OAAe,EAAEC,SAAiB,EAAW;EACjE,OACED,OAAO,CACJE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBC,KAAK,CAAC,IAAI;EACX;EAAA,CACCC,IAAI,CAAEC,IAAI,IAAKA,IAAI,KAAKJ,SAAS,IAAII,IAAI,KAAKJ,SAAS,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAElF;AAEO,SAASI,qBAAqBA,CAACC,WAAmB,EAAU;EACjE,OAAOC,eAAI,CAACC,IAAI,CAACF,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,CAAC;AACrE;AAEO,eAAeG,sBAAsBA,CAACH,WAAmB,EAAiB;EAC/E,MAAMI,eAAe,GAAGrC,KAAK,GAACsC,yBAAyB,CAACL,WAAW,CAAC;EACpE,MAAMM,aAAa,GAAGP,qBAAqB,CAACC,WAAW,CAAC;EAExD,MAAMO,aAAE,CAACC,QAAQ,CAACC,SAAS,CAACH,aAAa,EAAEI,+BAAY,CAAC;EAExD,MAAMC,kBAAkB,GAAG,MAAMJ,aAAE,CAACC,QAAQ,CAACI,QAAQ,CAACX,eAAI,CAACC,IAAI,CAACE,eAAe,CAAC,EAAE,MAAM,CAAC;EAEzF,MAAMS,iBAAiB,GAAGrB,YAAY,CAACmB,kBAAkB,EAAEpB,gBAAgB,CAAC;EAE5E,IAAI,CAACsB,iBAAiB,EAAE;IACtB,MAAMN,aAAE,CAACC,QAAQ,CAACC,SAAS,CACzBL,eAAe,EACd,GAAEO,kBAAkB,CAACG,IAAI,EAAG,KAAIvB,gBAAiB,IAAG,CACtD;EACH;AACF;AAEO,eAAewB,+BAA+BA,CAACf,WAAmB,EAAoB;EAC3F,MAAMI,eAAe,GAAGrC,KAAK,GAACsC,yBAAyB,CAACL,WAAW,CAAC;EACpE,MAAMM,aAAa,GAAGP,qBAAqB,CAACC,WAAW,CAAC;EAExD,MAAMgB,gBAAgB,GAAG,MAAMT,aAAE,CAACU,UAAU,CAACX,aAAa,CAAC;EAE3D,MAAMK,kBAAkB,GAAG,MAAMJ,aAAE,CAACC,QAAQ,CAACI,QAAQ,CAACX,eAAI,CAACC,IAAI,CAACE,eAAe,CAAC,EAAE,MAAM,CAAC;EACzF,MAAMS,iBAAiB,GAAGrB,YAAY,CAACmB,kBAAkB,EAAEpB,gBAAgB,CAAC;EAE5E,OAAOsB,iBAAiB,IAAIG,gBAAgB;AAC9C"}