<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14868" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14824"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Photos-->
        <scene sceneID="swb-gz-Gt9">
            <objects>
                <tableViewController storyboardIdentifier="QBAlbumsViewController" id="QL5-wR-LYt" customClass="QBAlbumsViewController" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="86" sectionHeaderHeight="22" sectionFooterHeight="22" id="66K-TS-Yoc">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                        <prototypes>
                            <tableViewCell contentMode="scaleToFill" selectionStyle="default" accessoryType="disclosureIndicator" indentationWidth="10" reuseIdentifier="AlbumCell" rowHeight="86" id="dSc-nm-apo" customClass="QBAlbumCell">
                                <rect key="frame" x="0.0" y="28" width="414" height="86"/>
                                <autoresizingMask key="autoresizingMask"/>
                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="dSc-nm-apo" id="7lA-qJ-5l4">
                                    <rect key="frame" x="0.0" y="0.0" width="382.5" height="86"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kTm-zK-fPP">
                                            <rect key="frame" x="28" y="7" width="68" height="72"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="aA6-Ye-jVF">
                                                    <rect key="frame" x="4" y="0.0" width="60" height="60"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="60" id="FN5-pM-4jN"/>
                                                        <constraint firstAttribute="height" constant="60" id="oOO-Jp-ELR"/>
                                                    </constraints>
                                                </imageView>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="7bp-En-8qW">
                                                    <rect key="frame" x="2" y="2" width="64" height="64"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="64" id="GXP-UA-8Q1"/>
                                                        <constraint firstAttribute="width" constant="64" id="Pl4-1t-5Nd"/>
                                                    </constraints>
                                                </imageView>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="FFV-lv-81k">
                                                    <rect key="frame" x="0.0" y="4" width="68" height="68"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="68" id="5Qb-Pu-leX"/>
                                                        <constraint firstAttribute="height" constant="68" id="sYi-5u-vyi"/>
                                                    </constraints>
                                                </imageView>
                                            </subviews>
                                            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="68" id="LPf-oG-4uz"/>
                                                <constraint firstAttribute="centerX" secondItem="aA6-Ye-jVF" secondAttribute="centerX" id="MGk-ae-eo0"/>
                                                <constraint firstAttribute="height" constant="72" id="QZm-fs-LRK"/>
                                                <constraint firstAttribute="centerX" secondItem="FFV-lv-81k" secondAttribute="centerX" id="YXS-g2-72j"/>
                                                <constraint firstItem="7bp-En-8qW" firstAttribute="top" secondItem="kTm-zK-fPP" secondAttribute="top" constant="2" id="eC1-Ul-L5i"/>
                                                <constraint firstItem="aA6-Ye-jVF" firstAttribute="top" secondItem="kTm-zK-fPP" secondAttribute="top" id="hKW-3j-Ktd"/>
                                                <constraint firstAttribute="bottom" secondItem="FFV-lv-81k" secondAttribute="bottom" id="mCq-ET-p0U"/>
                                                <constraint firstAttribute="centerX" secondItem="7bp-En-8qW" secondAttribute="centerX" id="p86-QG-SAY"/>
                                            </constraints>
                                        </view>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" misplaced="YES" text="Album Title" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="SeB-hL-cJJ">
                                            <rect key="frame" x="102" y="22" width="457" height="21"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" misplaced="YES" text="Number of Photos" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PTi-mB-UBX">
                                            <rect key="frame" x="102" y="46" width="457" height="15"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                    <constraints>
                                        <constraint firstAttribute="centerY" secondItem="kTm-zK-fPP" secondAttribute="centerY" id="6QC-SU-kOo"/>
                                        <constraint firstItem="SeB-hL-cJJ" firstAttribute="left" secondItem="kTm-zK-fPP" secondAttribute="right" constant="18" id="Whp-Ou-I3l"/>
                                        <constraint firstItem="SeB-hL-cJJ" firstAttribute="left" secondItem="PTi-mB-UBX" secondAttribute="left" id="b9V-nb-06z"/>
                                        <constraint firstItem="PTi-mB-UBX" firstAttribute="top" secondItem="SeB-hL-cJJ" secondAttribute="bottom" constant="3" id="ifo-Ls-t1q"/>
                                        <constraint firstItem="SeB-hL-cJJ" firstAttribute="right" secondItem="PTi-mB-UBX" secondAttribute="right" id="kPp-m3-EB1"/>
                                        <constraint firstItem="SeB-hL-cJJ" firstAttribute="top" secondItem="7lA-qJ-5l4" secondAttribute="topMargin" constant="14" id="qZJ-aU-Bes"/>
                                        <constraint firstAttribute="rightMargin" secondItem="SeB-hL-cJJ" secondAttribute="right" id="u0T-eN-4yh"/>
                                        <constraint firstItem="kTm-zK-fPP" firstAttribute="left" secondItem="7lA-qJ-5l4" secondAttribute="leftMargin" constant="8" id="zuV-Ye-IiO"/>
                                    </constraints>
                                </tableViewCellContentView>
                                <connections>
                                    <outlet property="countLabel" destination="PTi-mB-UBX" id="RE7-cn-ClQ"/>
                                    <outlet property="imageView1" destination="FFV-lv-81k" id="tDK-OF-ipY"/>
                                    <outlet property="imageView2" destination="7bp-En-8qW" id="2ol-my-8hB"/>
                                    <outlet property="imageView3" destination="aA6-Ye-jVF" id="jvN-vK-7sr"/>
                                    <outlet property="titleLabel" destination="SeB-hL-cJJ" id="KAO-l9-FAQ"/>
                                    <segue destination="QiH-NZ-ZGN" kind="show" id="2ft-L1-HaB"/>
                                </connections>
                            </tableViewCell>
                        </prototypes>
                        <connections>
                            <outlet property="dataSource" destination="QL5-wR-LYt" id="a4w-hM-ZqJ"/>
                            <outlet property="delegate" destination="QL5-wR-LYt" id="gZH-IY-ikI"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" title="Photos" id="r7D-Kp-3yC">
                        <barButtonItem key="leftBarButtonItem" systemItem="cancel" id="s1M-VA-CeM">
                            <connections>
                                <action selector="cancel:" destination="QL5-wR-LYt" id="l9K-7e-2qt"/>
                            </connections>
                        </barButtonItem>
                        <barButtonItem key="rightBarButtonItem" systemItem="done" id="laG-jk-Ma2">
                            <connections>
                                <action selector="done:" destination="QL5-wR-LYt" id="Try-7j-DzS"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="doneButton" destination="laG-jk-Ma2" id="CV6-AR-B90"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Qu9-YY-dvn" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="986" y="851"/>
        </scene>
        <!--Album Title-->
        <scene sceneID="ozY-oO-5JA">
            <objects>
                <collectionViewController storyboardIdentifier="QBAssetsViewController" id="QiH-NZ-ZGN" customClass="QBAssetsViewController" sceneMemberID="viewController">
                    <collectionView key="view" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" id="sD2-zK-ryo">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                        <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="2" minimumInteritemSpacing="2" id="6wp-N0-PIK">
                            <size key="itemSize" width="77.5" height="77.5"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="50" height="66"/>
                            <inset key="sectionInset" minX="0.0" minY="8" maxX="0.0" maxY="2"/>
                        </collectionViewFlowLayout>
                        <cells>
                            <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="AssetCell" id="fc0-k1-HNL" customClass="QBAssetCell">
                                <rect key="frame" x="0.0" y="8" width="77.5" height="77.5"/>
                                <autoresizingMask key="autoresizingMask"/>
                                <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                                    <rect key="frame" x="0.0" y="0.0" width="77.5" height="77.5"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="0aq-fn-r9R">
                                            <rect key="frame" x="0.0" y="0.0" width="77.5" height="77.5"/>
                                        </imageView>
                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BwJ-KE-LWZ" customClass="QBVideoIndicatorView">
                                            <rect key="frame" x="0.0" y="57.5" width="77.5" height="20"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rKh-6o-xw7" customClass="QBVideoIconView">
                                                    <rect key="frame" x="5" y="6" width="14" height="8"/>
                                                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="14" id="NjT-dW-eKX"/>
                                                        <constraint firstAttribute="height" constant="8" id="d0g-9I-F2s"/>
                                                    </constraints>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1We-cC-Ruu" customClass="QBSlomoIconView">
                                                    <rect key="frame" x="5" y="3" width="12" height="12"/>
                                                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="12" id="1qd-FB-w1Q"/>
                                                        <constraint firstAttribute="width" constant="12" id="iWE-jc-2Za"/>
                                                    </constraints>
                                                </view>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="00:00" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JBs-FX-TZQ">
                                                    <rect key="frame" x="23" y="2.5" width="49.5" height="15"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="centerY" secondItem="JBs-FX-TZQ" secondAttribute="centerY" id="2In-Kf-RYP"/>
                                                <constraint firstItem="JBs-FX-TZQ" firstAttribute="leading" secondItem="rKh-6o-xw7" secondAttribute="trailing" constant="4" id="8p9-Gp-fI0"/>
                                                <constraint firstAttribute="centerY" secondItem="rKh-6o-xw7" secondAttribute="centerY" id="NiG-4S-82w"/>
                                                <constraint firstAttribute="trailing" secondItem="JBs-FX-TZQ" secondAttribute="trailing" constant="5" id="UzI-el-RsC"/>
                                                <constraint firstItem="1We-cC-Ruu" firstAttribute="leading" secondItem="rKh-6o-xw7" secondAttribute="leading" id="bXl-KW-8bH"/>
                                                <constraint firstAttribute="height" constant="20" id="of9-Tc-hn8"/>
                                                <constraint firstItem="rKh-6o-xw7" firstAttribute="leading" secondItem="BwJ-KE-LWZ" secondAttribute="leading" constant="5" id="wdo-1w-Evf"/>
                                                <constraint firstItem="1We-cC-Ruu" firstAttribute="top" secondItem="rKh-6o-xw7" secondAttribute="top" constant="-3" id="wvh-r6-9Ty"/>
                                            </constraints>
                                            <connections>
                                                <outlet property="slomoIcon" destination="1We-cC-Ruu" id="6g0-tK-SgN"/>
                                                <outlet property="timeLabel" destination="JBs-FX-TZQ" id="MRp-1Z-69e"/>
                                                <outlet property="videoIcon" destination="rKh-6o-xw7" id="5VY-OW-Y7V"/>
                                            </connections>
                                        </view>
                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uyS-Tg-Iyl">
                                            <rect key="frame" x="0.0" y="0.0" width="77.5" height="77.5"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="m99-yj-HSc" customClass="QBCheckmarkView">
                                                    <rect key="frame" x="49.5" y="49.5" width="24" height="24"/>
                                                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="24" id="8Dq-6v-oj1"/>
                                                        <constraint firstAttribute="height" constant="24" id="bBP-zT-euh"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" red="1" green="1" blue="1" alpha="0.40000000000000002" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="m99-yj-HSc" secondAttribute="bottom" constant="4" id="Hyd-Pf-4Ni"/>
                                                <constraint firstAttribute="right" secondItem="m99-yj-HSc" secondAttribute="right" constant="4" id="aQv-HN-dQG"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                </view>
                                <constraints>
                                    <constraint firstItem="0aq-fn-r9R" firstAttribute="top" secondItem="fc0-k1-HNL" secondAttribute="top" id="1Cs-Ar-v4t"/>
                                    <constraint firstAttribute="trailing" secondItem="0aq-fn-r9R" secondAttribute="trailing" id="1xm-YR-1aF"/>
                                    <constraint firstAttribute="trailing" secondItem="uyS-Tg-Iyl" secondAttribute="trailing" id="1xt-jW-Drw"/>
                                    <constraint firstItem="uyS-Tg-Iyl" firstAttribute="leading" secondItem="fc0-k1-HNL" secondAttribute="leading" id="2gO-QS-g9M"/>
                                    <constraint firstItem="0aq-fn-r9R" firstAttribute="leading" secondItem="fc0-k1-HNL" secondAttribute="leading" id="7WM-zv-4jB"/>
                                    <constraint firstAttribute="bottom" secondItem="uyS-Tg-Iyl" secondAttribute="bottom" id="B4l-PZ-gqc"/>
                                    <constraint firstItem="BwJ-KE-LWZ" firstAttribute="top" secondItem="fc0-k1-HNL" secondAttribute="top" id="UJz-U6-yN2"/>
                                    <constraint firstAttribute="trailing" secondItem="BwJ-KE-LWZ" secondAttribute="trailing" id="ViU-xg-jBz"/>
                                    <constraint firstItem="BwJ-KE-LWZ" firstAttribute="leading" secondItem="fc0-k1-HNL" secondAttribute="leading" id="cOY-Et-5UE"/>
                                    <constraint firstAttribute="bottom" secondItem="0aq-fn-r9R" secondAttribute="bottom" id="iIA-2y-gLa"/>
                                    <constraint firstAttribute="bottom" secondItem="BwJ-KE-LWZ" secondAttribute="bottom" id="jYP-8U-0SU"/>
                                    <constraint firstItem="uyS-Tg-Iyl" firstAttribute="top" secondItem="fc0-k1-HNL" secondAttribute="top" id="q37-T4-cB2"/>
                                </constraints>
                                <variation key="default">
                                    <mask key="constraints">
                                        <exclude reference="UJz-U6-yN2"/>
                                    </mask>
                                </variation>
                                <connections>
                                    <outlet property="imageView" destination="0aq-fn-r9R" id="smK-ma-TWL"/>
                                    <outlet property="overlayView" destination="uyS-Tg-Iyl" id="N6m-w2-m4M"/>
                                    <outlet property="videoIndicatorView" destination="BwJ-KE-LWZ" id="HkB-Dc-nzF"/>
                                </connections>
                            </collectionViewCell>
                        </cells>
                        <collectionReusableView key="sectionFooterView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="FooterView" id="sqR-h3-lW1">
                            <rect key="frame" x="0.0" y="87.5" width="414" height="66"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" tag="1" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Number of Photos and Videos" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="m2s-7T-bnL">
                                    <rect key="frame" x="0.0" y="22.5" width="414" height="21"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <constraints>
                                <constraint firstItem="m2s-7T-bnL" firstAttribute="left" secondItem="sqR-h3-lW1" secondAttribute="left" id="8fv-Sl-lhe"/>
                                <constraint firstAttribute="centerY" secondItem="m2s-7T-bnL" secondAttribute="centerY" id="DrY-pC-Gfi"/>
                                <constraint firstAttribute="right" secondItem="m2s-7T-bnL" secondAttribute="right" id="lng-sP-3aa"/>
                            </constraints>
                        </collectionReusableView>
                        <connections>
                            <outlet property="dataSource" destination="QiH-NZ-ZGN" id="PlE-FW-LqS"/>
                            <outlet property="delegate" destination="QiH-NZ-ZGN" id="0SZ-eY-tkn"/>
                        </connections>
                    </collectionView>
                    <navigationItem key="navigationItem" title="Album Title" id="wKf-eb-U1x">
                        <barButtonItem key="rightBarButtonItem" systemItem="done" id="nai-ZV-lR8">
                            <connections>
                                <action selector="done:" destination="QiH-NZ-ZGN" id="RYV-Ik-8ry"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="doneButton" destination="nai-ZV-lR8" id="lxY-18-MpF"/>
                    </connections>
                </collectionViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="qBb-2Q-SxP" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1814" y="852"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="TBV-v4-1AQ">
            <objects>
                <navigationController storyboardIdentifier="QBAlbumsNavigationController" id="8Dq-FL-Kai" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="Whc-Ry-EBq">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <toolbar key="toolbar" opaque="NO" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="pNX-sT-Qig">
                        <autoresizingMask key="autoresizingMask"/>
                    </toolbar>
                    <connections>
                        <segue destination="QL5-wR-LYt" kind="relationship" relationship="rootViewController" id="fhp-Wj-h79"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ag3-Vu-ohc" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="166" y="852"/>
        </scene>
    </scenes>
</document>
