{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n  return new Observable(subscriber => {\n    const resource = resourceFactory();\n    const result = observableFactory(resource);\n    const source = result ? innerFrom(result) : EMPTY;\n    source.subscribe(subscriber);\n    return () => {\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}\n//# sourceMappingURL=using.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}