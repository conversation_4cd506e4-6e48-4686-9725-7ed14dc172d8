{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/table/testing/cell-harness.ts", "../../../../../../../src/material/table/testing/row-harness.ts", "../../../../../../../src/material/table/testing/table-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {CellHarnessFilters} from './table-harness-filters';\n\nexport abstract class _MatCellHarnessBase extends ContentContainerComponentHarness {\n  /** Gets the cell's text. */\n  async getText(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Gets the name of the column that the cell belongs to. */\n  async getColumnName(): Promise<string> {\n    const host = await this.host();\n    const classAttribute = await host.getAttribute('class');\n\n    if (classAttribute) {\n      const prefix = 'mat-column-';\n      const name = classAttribute\n        .split(' ')\n        .map(c => c.trim())\n        .find(c => c.startsWith(prefix));\n\n      if (name) {\n        return name.split(prefix)[1];\n      }\n    }\n\n    throw Error('Could not determine column name of cell.');\n  }\n\n  protected static _getCellPredicate<T extends MatCellHarness>(\n    type: ComponentHarnessConstructor<T>,\n    options: CellHarnessFilters,\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(type, options)\n      .addOption('text', options.text, (harness, text) =>\n        HarnessPredicate.stringMatches(harness.getText(), text),\n      )\n      .addOption('columnName', options.columnName, (harness, name) =>\n        HarnessPredicate.stringMatches(harness.getColumnName(), name),\n      );\n  }\n}\n\n/** Harness for interacting with an Angular Material table cell. */\nexport class MatCellHarness extends _MatCellHarnessBase {\n  /** The selector for the host element of a `MatCellHarness` instance. */\n  static hostSelector = '.mat-mdc-cell';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a table cell with specific attributes.\n   * @param options Options for narrowing the search\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: CellHarnessFilters = {}): HarnessPredicate<MatCellHarness> {\n    return _MatCellHarnessBase._getCellPredicate(this, options);\n  }\n}\n\n/** Harness for interacting with an Angular Material table header cell. */\nexport class MatHeaderCellHarness extends _MatCellHarnessBase {\n  /** The selector for the host element of a `MatHeaderCellHarness` instance. */\n  static hostSelector = '.mat-mdc-header-cell';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a table header cell with specific\n   * attributes.\n   * @param options Options for narrowing the search\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: CellHarnessFilters = {}): HarnessPredicate<MatHeaderCellHarness> {\n    return _MatCellHarnessBase._getCellPredicate(this, options);\n  }\n}\n\n/** Harness for interacting with an Angular Material table footer cell. */\nexport class MatFooterCellHarness extends _MatCellHarnessBase {\n  /** The selector for the host element of a `MatFooterCellHarness` instance. */\n  static hostSelector = '.mat-mdc-footer-cell';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a table footer cell with specific\n   * attributes.\n   * @param options Options for narrowing the search\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: CellHarnessFilters = {}): HarnessPredicate<MatFooterCellHarness> {\n    return _MatCellHarnessBase._getCellPredicate(this, options);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n  parallel,\n} from '@angular/cdk/testing';\nimport {\n  _MatCellHarnessBase,\n  MatCellHarness,\n  MatFooterCellHarness,\n  MatHeaderCellHarness,\n} from './cell-harness';\nimport {CellHarnessFilters, RowHarnessFilters} from './table-harness-filters';\n\n/** Text extracted from a table row organized by columns. */\nexport interface MatRowHarnessColumnsText {\n  [columnName: string]: string;\n}\n\nexport abstract class _MatRowHarnessBase<\n  CellType extends ComponentHarnessConstructor<Cell> & {\n    with: (options?: CellHarnessFilters) => HarnessPredicate<Cell>;\n  },\n  Cell extends _MatCellHarnessBase,\n> extends ComponentHarness {\n  protected abstract _cellHarness: CellType;\n\n  /** Gets a list of `MatCellHarness` for all cells in the row. */\n  async getCells(filter: CellHarnessFilters = {}): Promise<Cell[]> {\n    return this.locatorForAll(this._cellHarness.with(filter))();\n  }\n\n  /** Gets the text of the cells in the row. */\n  async getCellTextByIndex(filter: CellHarnessFilters = {}): Promise<string[]> {\n    const cells = await this.getCells(filter);\n    return parallel(() => cells.map(cell => cell.getText()));\n  }\n\n  /** Gets the text inside the row organized by columns. */\n  async getCellTextByColumnName(): Promise<MatRowHarnessColumnsText> {\n    const output: MatRowHarnessColumnsText = {};\n    const cells = await this.getCells();\n    const cellsData = await parallel(() =>\n      cells.map(cell => {\n        return parallel(() => [cell.getColumnName(), cell.getText()]);\n      }),\n    );\n    cellsData.forEach(([columnName, text]) => (output[columnName] = text));\n    return output;\n  }\n}\n\n/** Harness for interacting with an Angular Material table row. */\nexport class MatRowHarness extends _MatRowHarnessBase<typeof MatCellHarness, MatCellHarness> {\n  /** The selector for the host element of a `MatRowHarness` instance. */\n  static hostSelector = '.mat-mdc-row';\n  protected _cellHarness = MatCellHarness;\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a table row with specific attributes.\n   * @param options Options for narrowing the search\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatRowHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: RowHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n}\n\n/** Harness for interacting with an Angular Material table header row. */\nexport class MatHeaderRowHarness extends _MatRowHarnessBase<\n  typeof MatHeaderCellHarness,\n  MatHeaderCellHarness\n> {\n  /** The selector for the host element of a `MatHeaderRowHarness` instance. */\n  static hostSelector = '.mat-mdc-header-row';\n  protected _cellHarness = MatHeaderCellHarness;\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a table header row with specific\n   * attributes.\n   * @param options Options for narrowing the search\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatHeaderRowHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: RowHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n}\n\n/** Harness for interacting with an Angular Material table footer row. */\nexport class MatFooterRowHarness extends _MatRowHarnessBase<\n  typeof MatFooterCellHarness,\n  MatFooterCellHarness\n> {\n  /** The selector for the host element of a `MatFooterRowHarness` instance. */\n  static hostSelector = '.mat-mdc-footer-row';\n  protected _cellHarness = MatFooterCellHarness;\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a table footer row cell with specific\n   * attributes.\n   * @param options Options for narrowing the search\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatFooterRowHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: RowHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n  parallel,\n} from '@angular/cdk/testing';\nimport {\n  MatFooterRowHarness,\n  MatHeaderRowHarness,\n  MatRowHarness,\n  MatRowHarnessColumnsText,\n} from './row-harness';\nimport {RowHarnessFilters, TableHarnessFilters} from './table-harness-filters';\n\n/** Text extracted from a table organized by columns. */\nexport interface MatTableHarnessColumnsText {\n  [columnName: string]: {\n    text: string[];\n    headerText: string[];\n    footerText: string[];\n  };\n}\n\n/** Harness for interacting with a mat-table in tests. */\nexport class MatTableHarness extends ContentContainerComponentHarness<string> {\n  /** The selector for the host element of a `MatTableHarness` instance. */\n  static hostSelector = '.mat-mdc-table';\n  _headerRowHarness = MatHeaderRowHarness;\n  _rowHarness = MatRowHarness;\n  private _footerRowHarness = MatFooterRowHarness;\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a table with specific attributes.\n   * @param options Options for narrowing the search\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTableHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TableHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Gets all the header rows in a table. */\n  async getHeaderRows(filter: RowHarnessFilters = {}): Promise<MatHeaderRowHarness[]> {\n    return this.locatorForAll(this._headerRowHarness.with(filter))();\n  }\n\n  /** Gets all the regular data rows in a table. */\n  async getRows(filter: RowHarnessFilters = {}): Promise<MatRowHarness[]> {\n    return this.locatorForAll(this._rowHarness.with(filter))();\n  }\n\n  /** Gets all the footer rows in a table. */\n  async getFooterRows(filter: RowHarnessFilters = {}): Promise<MatFooterRowHarness[]> {\n    return this.locatorForAll(this._footerRowHarness.with(filter))();\n  }\n\n  /** Gets the text inside the entire table organized by rows. */\n  async getCellTextByIndex(): Promise<string[][]> {\n    const rows = await this.getRows();\n    return parallel(() => rows.map(row => row.getCellTextByIndex()));\n  }\n\n  /** Gets the text inside the entire table organized by columns. */\n  async getCellTextByColumnName(): Promise<MatTableHarnessColumnsText> {\n    const [headerRows, footerRows, dataRows] = await parallel(() => [\n      this.getHeaderRows(),\n      this.getFooterRows(),\n      this.getRows(),\n    ]);\n\n    const text: MatTableHarnessColumnsText = {};\n    const [headerData, footerData, rowsData] = await parallel(() => [\n      parallel(() => headerRows.map(row => row.getCellTextByColumnName())),\n      parallel(() => footerRows.map(row => row.getCellTextByColumnName())),\n      parallel(() => dataRows.map(row => row.getCellTextByColumnName())),\n    ]);\n\n    rowsData.forEach(data => {\n      Object.keys(data).forEach(columnName => {\n        const cellText = data[columnName];\n\n        if (!text[columnName]) {\n          text[columnName] = {\n            headerText: getCellTextsByColumn(headerData, columnName),\n            footerText: getCellTextsByColumn(footerData, columnName),\n            text: [],\n          };\n        }\n\n        text[columnName].text.push(cellText);\n      });\n    });\n\n    return text;\n  }\n}\n\n/** Extracts the text of cells only under a particular column. */\nfunction getCellTextsByColumn(rowsData: MatRowHarnessColumnsText[], column: string): string[] {\n  const columnTexts: string[] = [];\n\n  rowsData.forEach(data => {\n    Object.keys(data).forEach(columnName => {\n      if (columnName === column) {\n        columnTexts.push(data[columnName]);\n      }\n    });\n  });\n\n  return columnTexts;\n}\n"], "names": [], "mappings": ";;AAeM,MAAgB,mBAAoB,SAAQ,gCAAgC,CAAA;;AAEhF,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;KACnC;;AAGD,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAExD,IAAI,cAAc,EAAE;YAClB,MAAM,MAAM,GAAG,aAAa,CAAC;YAC7B,MAAM,IAAI,GAAG,cAAc;iBACxB,KAAK,CAAC,GAAG,CAAC;iBACV,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;AAClB,iBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YAEnC,IAAI,IAAI,EAAE;gBACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9B;SACF;AAED,QAAA,MAAM,KAAK,CAAC,0CAA0C,CAAC,CAAC;KACzD;AAES,IAAA,OAAO,iBAAiB,CAChC,IAAoC,EACpC,OAA2B,EAAA;AAE3B,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;aACvC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CACxD;aACA,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,IAAI,KACzD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,CAC9D,CAAC;KACL;AACF,CAAA;AAED;AACM,MAAO,cAAe,SAAQ,mBAAmB,CAAA;;aAE9C,IAAY,CAAA,YAAA,GAAG,eAAe,CAAC,EAAA;AAEtC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA8B,EAAE,EAAA;QAC1C,OAAO,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC7D;;AAGH;AACM,MAAO,oBAAqB,SAAQ,mBAAmB,CAAA;;aAEpD,IAAY,CAAA,YAAA,GAAG,sBAAsB,CAAC,EAAA;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA8B,EAAE,EAAA;QAC1C,OAAO,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC7D;;AAGH;AACM,MAAO,oBAAqB,SAAQ,mBAAmB,CAAA;;aAEpD,IAAY,CAAA,YAAA,GAAG,sBAAsB,CAAC,EAAA;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA8B,EAAE,EAAA;QAC1C,OAAO,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC7D;;;ACxEG,MAAgB,kBAKpB,SAAQ,gBAAgB,CAAA;;AAIxB,IAAA,MAAM,QAAQ,CAAC,MAAA,GAA6B,EAAE,EAAA;AAC5C,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAC7D;;AAGD,IAAA,MAAM,kBAAkB,CAAC,MAAA,GAA6B,EAAE,EAAA;QACtD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1C,QAAA,OAAO,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;KAC1D;;AAGD,IAAA,MAAM,uBAAuB,GAAA;QAC3B,MAAM,MAAM,GAA6B,EAAE,CAAC;AAC5C,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpC,QAAA,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,MAC/B,KAAK,CAAC,GAAG,CAAC,IAAI,IAAG;AACf,YAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;SAC/D,CAAC,CACH,CAAC;QACF,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACvE,QAAA,OAAO,MAAM,CAAC;KACf;AACF,CAAA;AAED;AACM,MAAO,aAAc,SAAQ,kBAAyD,CAAA;AAA5F,IAAA,WAAA,GAAA;;QAGY,IAAY,CAAA,YAAA,GAAG,cAAc,CAAC;KAazC;;aAdQ,IAAY,CAAA,YAAA,GAAG,cAAH,CAAkB,EAAA;AAGrC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAA6B,EAAE,EAAA;AAE/B,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;AAGH;AACM,MAAO,mBAAoB,SAAQ,kBAGxC,CAAA;AAHD,IAAA,WAAA,GAAA;;QAMY,IAAY,CAAA,YAAA,GAAG,oBAAoB,CAAC;KAc/C;;aAfQ,IAAY,CAAA,YAAA,GAAG,qBAAH,CAAyB,EAAA;AAG5C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAA6B,EAAE,EAAA;AAE/B,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;AAGH;AACM,MAAO,mBAAoB,SAAQ,kBAGxC,CAAA;AAHD,IAAA,WAAA,GAAA;;QAMY,IAAY,CAAA,YAAA,GAAG,oBAAoB,CAAC;KAc/C;;aAfQ,IAAY,CAAA,YAAA,GAAG,qBAAH,CAAyB,EAAA;AAG5C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAA6B,EAAE,EAAA;AAE/B,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;;AC3FH;AACM,MAAO,eAAgB,SAAQ,gCAAwC,CAAA;AAA7E,IAAA,WAAA,GAAA;;QAGE,IAAiB,CAAA,iBAAA,GAAG,mBAAmB,CAAC;QACxC,IAAW,CAAA,WAAA,GAAG,aAAa,CAAC;QACpB,IAAiB,CAAA,iBAAA,GAAG,mBAAmB,CAAC;KAoEjD;;aAvEQ,IAAY,CAAA,YAAA,GAAG,gBAAH,CAAoB,EAAA;AAKvC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAA+B,EAAE,EAAA;AAEjC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;AAGD,IAAA,MAAM,aAAa,CAAC,MAAA,GAA4B,EAAE,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAClE;;AAGD,IAAA,MAAM,OAAO,CAAC,MAAA,GAA4B,EAAE,EAAA;AAC1C,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAC5D;;AAGD,IAAA,MAAM,aAAa,CAAC,MAAA,GAA4B,EAAE,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAClE;;AAGD,IAAA,MAAM,kBAAkB,GAAA;AACtB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AAClC,QAAA,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;KAClE;;AAGD,IAAA,MAAM,uBAAuB,GAAA;AAC3B,QAAA,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;YAC9D,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,OAAO,EAAE;AACf,SAAA,CAAC,CAAC;QAEH,MAAM,IAAI,GAA+B,EAAE,CAAC;AAC5C,QAAA,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;AAC9D,YAAA,QAAQ,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,uBAAuB,EAAE,CAAC,CAAC;AACpE,YAAA,QAAQ,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,uBAAuB,EAAE,CAAC,CAAC;AACpE,YAAA,QAAQ,CAAC,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,uBAAuB,EAAE,CAAC,CAAC;AACnE,SAAA,CAAC,CAAC;AAEH,QAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAG;YACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,IAAG;AACrC,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;AAElC,gBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBACrB,IAAI,CAAC,UAAU,CAAC,GAAG;AACjB,wBAAA,UAAU,EAAE,oBAAoB,CAAC,UAAU,EAAE,UAAU,CAAC;AACxD,wBAAA,UAAU,EAAE,oBAAoB,CAAC,UAAU,EAAE,UAAU,CAAC;AACxD,wBAAA,IAAI,EAAE,EAAE;qBACT,CAAC;iBACH;gBAED,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACvC,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC;KACb;;AAGH;AACA,SAAS,oBAAoB,CAAC,QAAoC,EAAE,MAAc,EAAA;IAChF,MAAM,WAAW,GAAa,EAAE,CAAC;AAEjC,IAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAG;QACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,IAAG;AACrC,YAAA,IAAI,UAAU,KAAK,MAAM,EAAE;gBACzB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;aACpC;AACH,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,WAAW,CAAC;AACrB;;;;"}