// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		3400A8161CEB54F3008A0BC7 /* ImageCropPicker.m in Sources */ = {isa = PBXBuildFile; fileRef = 3400A8151CEB54F3008A0BC7 /* ImageCropPicker.m */; };
		3408F5F11E0DE76F00E97159 /* Compression.m in Sources */ = {isa = PBXBuildFile; fileRef = 3408F5F01E0DE76F00E97159 /* Compression.m */; };
		34963AB11D6B96A800F9CA2F /* UIImage+Resize.m in Sources */ = {isa = PBXBuildFile; fileRef = 34963A941D6B919800F9CA2F /* UIImage+Resize.m */; };
		34EC4AB51D78FEC6001E9E86 /* QBImagePicker.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 34434E2E1D6F5EA300BF5063 /* QBImagePicker.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		34434E2D1D6F5EA300BF5063 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 34434E261D6F5EA300BF5063 /* QBImagePicker.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = AAA8FE031ACDA079002A9710;
			remoteInfo = QBImagePicker;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		3400A8081CEB54A6008A0BC7 /* libimageCropPicker.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libimageCropPicker.a; sourceTree = BUILT_PRODUCTS_DIR; };
		3400A8141CEB54F3008A0BC7 /* ImageCropPicker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ImageCropPicker.h; sourceTree = "<group>"; };
		3400A8151CEB54F3008A0BC7 /* ImageCropPicker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ImageCropPicker.m; sourceTree = "<group>"; };
		3408F5EF1E0DE76F00E97159 /* Compression.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Compression.h; sourceTree = "<group>"; };
		3408F5F01E0DE76F00E97159 /* Compression.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Compression.m; sourceTree = "<group>"; };
		34434E261D6F5EA300BF5063 /* QBImagePicker.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = QBImagePicker.xcodeproj; path = QBImagePicker/QBImagePicker.xcodeproj; sourceTree = "<group>"; };
		347ACA051E4B2B2F0068D500 /* libRCTImage.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libRCTImage.a; path = "../../../../Library/Developer/Xcode/DerivedData/example-grvsvunjwoajzcfynrlckgmefufr/Build/Products/Debug-iphonesimulator/libRCTImage.a"; sourceTree = "<group>"; };
		34963A931D6B919800F9CA2F /* UIImage+Resize.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Resize.h"; sourceTree = "<group>"; };
		34963A941D6B919800F9CA2F /* UIImage+Resize.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Resize.m"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		3400A8051CEB54A6008A0BC7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				34EC4AB51D78FEC6001E9E86 /* QBImagePicker.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3400A7FF1CEB54A6008A0BC7 = {
			isa = PBXGroup;
			children = (
				34ECC8E3204DEEA90053BBC0 /* src */,
				34434E251D6F5E5600BF5063 /* Libraries */,
				3400A8091CEB54A6008A0BC7 /* Products */,
				347ACA041E4B2B2F0068D500 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		3400A8091CEB54A6008A0BC7 /* Products */ = {
			isa = PBXGroup;
			children = (
				3400A8081CEB54A6008A0BC7 /* libimageCropPicker.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		34434E251D6F5E5600BF5063 /* Libraries */ = {
			isa = PBXGroup;
			children = (
				34434E261D6F5EA300BF5063 /* QBImagePicker.xcodeproj */,
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		34434E271D6F5EA300BF5063 /* Products */ = {
			isa = PBXGroup;
			children = (
				34434E2E1D6F5EA300BF5063 /* QBImagePicker.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		347ACA041E4B2B2F0068D500 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				347ACA051E4B2B2F0068D500 /* libRCTImage.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		34ECC8E3204DEEA90053BBC0 /* src */ = {
			isa = PBXGroup;
			children = (
				3408F5EF1E0DE76F00E97159 /* Compression.h */,
				3408F5F01E0DE76F00E97159 /* Compression.m */,
				3400A8141CEB54F3008A0BC7 /* ImageCropPicker.h */,
				3400A8151CEB54F3008A0BC7 /* ImageCropPicker.m */,
				34963A931D6B919800F9CA2F /* UIImage+Resize.h */,
				34963A941D6B919800F9CA2F /* UIImage+Resize.m */,
			);
			path = src;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		3400A8071CEB54A6008A0BC7 /* imageCropPicker */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3400A8111CEB54A6008A0BC7 /* Build configuration list for PBXNativeTarget "imageCropPicker" */;
			buildPhases = (
				3400A8041CEB54A6008A0BC7 /* Sources */,
				3400A8051CEB54A6008A0BC7 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = imageCropPicker;
			productName = picker;
			productReference = 3400A8081CEB54A6008A0BC7 /* libimageCropPicker.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		3400A8001CEB54A6008A0BC7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0730;
				ORGANIZATIONNAME = "Ivan Pusic";
				TargetAttributes = {
					3400A8071CEB54A6008A0BC7 = {
						CreatedOnToolsVersion = 7.3.1;
					};
				};
			};
			buildConfigurationList = 3400A8031CEB54A6008A0BC7 /* Build configuration list for PBXProject "imageCropPicker" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				de,
				es,
				fr,
				ja,
				pl,
				"zh-Hans",
				nb,
				vi,
				da,
				fi,
			);
			mainGroup = 3400A7FF1CEB54A6008A0BC7;
			productRefGroup = 3400A8091CEB54A6008A0BC7 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 34434E271D6F5EA300BF5063 /* Products */;
					ProjectRef = 34434E261D6F5EA300BF5063 /* QBImagePicker.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				3400A8071CEB54A6008A0BC7 /* imageCropPicker */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		34434E2E1D6F5EA300BF5063 /* QBImagePicker.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = QBImagePicker.framework;
			remoteRef = 34434E2D1D6F5EA300BF5063 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXSourcesBuildPhase section */
		3400A8041CEB54A6008A0BC7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3408F5F11E0DE76F00E97159 /* Compression.m in Sources */,
				34963AB11D6B96A800F9CA2F /* UIImage+Resize.m in Sources */,
				3400A8161CEB54F3008A0BC7 /* ImageCropPicker.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		3400A80F1CEB54A6008A0BC7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		3400A8101CEB54A6008A0BC7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		3400A8121CEB54A6008A0BC7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				EMBEDDED_CONTENT_CONTAINS_SWIFT = NO;
				FRAMEWORK_SEARCH_PATHS = "";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../react-native/Libraries/Image/**",
				);
				LD_RUNPATH_SEARCH_PATHS = "";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = imageCropPicker;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		3400A8131CEB54A6008A0BC7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				EMBEDDED_CONTENT_CONTAINS_SWIFT = NO;
				FRAMEWORK_SEARCH_PATHS = "";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../react-native/Libraries/Image/**",
				);
				LD_RUNPATH_SEARCH_PATHS = "";
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = imageCropPicker;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3400A8031CEB54A6008A0BC7 /* Build configuration list for PBXProject "imageCropPicker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3400A80F1CEB54A6008A0BC7 /* Debug */,
				3400A8101CEB54A6008A0BC7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3400A8111CEB54A6008A0BC7 /* Build configuration list for PBXNativeTarget "imageCropPicker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3400A8121CEB54A6008A0BC7 /* Debug */,
				3400A8131CEB54A6008A0BC7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 3400A8001CEB54A6008A0BC7 /* Project object */;
}
