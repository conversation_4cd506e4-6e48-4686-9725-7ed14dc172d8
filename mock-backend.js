const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 8001;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data
const mockStats = {
  users: { total: 150, active: 120, new_today: 5 },
  products: { total: 500, published: 450, out_of_stock: 10, low_stock: 25 },
  orders: { total: 1200, pending: 15, processing: 8, delivered: 1177, total_revenue: 45000, orders_today: 12, revenue_today: 850 },
  categories: { total: 25, active: 22, featured: 8 }
};

const mockUser = {
  id: 1,
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'admin'
};

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'E-Commerce Backend API Mock Server',
    version: '1.0.0',
    status: 'running',
    endpoints: [
      'GET /api/auth/user',
      'POST /api/auth/login',
      'GET /api/dashboard/stats',
      'GET /api/users/statistics',
      'GET /api/products/statistics',
      'GET /api/orders/statistics',
      'GET /api/categories/statistics'
    ]
  });
});

// Auth routes
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  if (email && password) {
    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: mockUser,
        token: 'mock-jwt-token-' + Date.now()
      }
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }
});

app.get('/api/auth/user', (req, res) => {
  res.json({
    success: true,
    data: mockUser
  });
});

app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Dashboard routes
app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    success: true,
    data: mockStats
  });
});

// Statistics routes
app.get('/api/users/statistics', (req, res) => {
  res.json({
    success: true,
    data: mockStats.users
  });
});

app.get('/api/products/statistics', (req, res) => {
  res.json({
    success: true,
    data: mockStats.products
  });
});

app.get('/api/orders/statistics', (req, res) => {
  res.json({
    success: true,
    data: mockStats.orders
  });
});

app.get('/api/categories/statistics', (req, res) => {
  res.json({
    success: true,
    data: mockStats.categories
  });
});

// Activity log
app.get('/api/activity-log', (req, res) => {
  const activities = [
    {
      id: 1,
      description: 'New user registered',
      subject_type: 'App\\Models\\User',
      subject_id: 123,
      causer: { id: 1, name: 'System' },
      created_at: new Date().toISOString()
    },
    {
      id: 2,
      description: 'Product updated',
      subject_type: 'App\\Models\\Product',
      subject_id: 456,
      causer: { id: 2, name: 'Admin' },
      created_at: new Date(Date.now() - 3600000).toISOString()
    }
  ];
  
  res.json({
    success: true,
    data: activities
  });
});

// Analytics routes
app.get('/api/analytics/sales-chart', (req, res) => {
  const days = parseInt(req.query.days) || 7;
  const values = Array.from({ length: days }, () => Math.floor(Math.random() * 1000) + 500);
  
  res.json({
    success: true,
    data: {
      labels: Array.from({ length: days }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (days - 1 - i));
        return date.toLocaleDateString();
      }),
      values: values
    }
  });
});

app.get('/api/analytics/orders-chart', (req, res) => {
  const days = parseInt(req.query.days) || 7;
  const values = Array.from({ length: days }, () => Math.floor(Math.random() * 50) + 10);
  
  res.json({
    success: true,
    data: {
      labels: Array.from({ length: days }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (days - 1 - i));
        return date.toLocaleDateString();
      }),
      values: values
    }
  });
});

// Catch all other routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    requested_url: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Mock Backend Server running on http://localhost:${PORT}`);
  console.log(`📊 Dashboard API available at http://localhost:${PORT}/api/dashboard/stats`);
  console.log(`🔐 Auth API available at http://localhost:${PORT}/api/auth/login`);
  console.log(`📈 Analytics API available at http://localhost:${PORT}/api/analytics/sales-chart`);
  console.log('');
  console.log('✅ Backend is ready! Now start the frontend with: cd frontend && npm start');
});

module.exports = app;
