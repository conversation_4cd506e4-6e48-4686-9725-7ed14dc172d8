{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { timeout } from './timeout';\nexport function timeoutWith(due, withObservable, scheduler) {\n  let first;\n  let each;\n  let _with;\n  scheduler = scheduler !== null && scheduler !== void 0 ? scheduler : async;\n  if (isValidDate(due)) {\n    first = due;\n  } else if (typeof due === 'number') {\n    each = due;\n  }\n  if (withObservable) {\n    _with = () => withObservable;\n  } else {\n    throw new TypeError('No observable provided to switch to');\n  }\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n  return timeout({\n    first,\n    each,\n    scheduler,\n    with: _with\n  });\n}\n//# sourceMappingURL=timeoutWith.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}