{"version": 3, "file": "withIosSplashInfoPlist.js", "names": ["_configPlugins", "data", "require", "_debug", "_interopRequireDefault", "obj", "__esModule", "default", "debug", "Debug", "withIosSplashInfoPlist", "config", "splash", "withInfoPlist", "modResults", "setSplashInfoPlist", "exports", "infoPlist", "_splash$dark", "_splash$dark2", "_splash$dark3", "_splash$dark4", "isDarkModeEnabled", "dark", "image", "tabletImage", "backgroundColor", "tabletBackgroundColor", "_config$ios$userInter", "_config$ios", "existing", "ios", "userInterfaceStyle", "WarningAggregator", "addWarningIOS", "UIUserInterfaceStyle", "UILaunchStoryboardName"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashInfoPlist.ts"], "sourcesContent": ["import { ConfigPlugin, InfoPlist, WarningAggregator, withInfoPlist } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport Debug from 'debug';\n\nimport { IOSSplashConfig } from './getIosSplashConfig';\n\nconst debug = Debug('expo:prebuild-config:expo-splash-screen:ios:infoPlist');\n\nexport const withIosSplashInfoPlist: ConfigPlugin<IOSSplashConfig> = (config, splash) => {\n  return withInfoPlist(config, (config) => {\n    config.modResults = setSplashInfoPlist(config, config.modResults, splash);\n    return config;\n  });\n};\n\nexport function setSplashInfoPlist(\n  config: ExpoConfig,\n  infoPlist: InfoPlist,\n  splash: IOSSplashConfig\n): InfoPlist {\n  const isDarkModeEnabled = !!(\n    splash?.dark?.image ||\n    splash?.dark?.tabletImage ||\n    splash?.dark?.backgroundColor ||\n    splash?.dark?.tabletBackgroundColor\n  );\n  debug(`isDarkModeEnabled: `, isDarkModeEnabled);\n\n  if (isDarkModeEnabled) {\n    // IOSConfig.UserInterfaceStyle.getUserInterfaceStyle(config);\n    // Determine if the user manually defined the userInterfaceStyle incorrectly\n    const existing = config.ios?.userInterfaceStyle ?? config.userInterfaceStyle;\n    // Add a warning to prevent the dark mode splash screen from not being shown -- this was learned the hard way.\n    if (existing && existing !== 'automatic') {\n      WarningAggregator.addWarningIOS(\n        'userInterfaceStyle',\n        'The existing `userInterfaceStyle` property is preventing splash screen from working properly. Please remove it or disable dark mode splash screens.'\n      );\n    }\n    // assigning it to auto anyways, but this is fragile because the order of operations matter now\n    infoPlist.UIUserInterfaceStyle = 'Automatic';\n  } else {\n    // NOTE(brentvatne): Commented out this line because it causes https://github.com/expo/expo-cli/issues/3935\n    // We should revisit this approach.\n    // delete infoPlist.UIUserInterfaceStyle;\n  }\n\n  if (splash) {\n    // TODO: What to do here ??\n    infoPlist.UILaunchStoryboardName = 'SplashScreen';\n  } else {\n    debug(`Disabling UILaunchStoryboardName`);\n    delete infoPlist.UILaunchStoryboardName;\n  }\n\n  return infoPlist;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0B,SAAAG,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAI1B,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,uDAAuD,CAAC;AAErE,MAAMC,sBAAqD,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACvF,OAAO,IAAAC,8BAAa,EAACF,MAAM,EAAGA,MAAM,IAAK;IACvCA,MAAM,CAACG,UAAU,GAAGC,kBAAkB,CAACJ,MAAM,EAAEA,MAAM,CAACG,UAAU,EAAEF,MAAM,CAAC;IACzE,OAAOD,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAN,sBAAA,GAAAA,sBAAA;AAEK,SAASK,kBAAkBA,CAChCJ,MAAkB,EAClBM,SAAoB,EACpBL,MAAuB,EACZ;EAAA,IAAAM,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA;EACX,MAAMC,iBAAiB,GAAG,CAAC,EACzBV,MAAM,aAANA,MAAM,gBAAAM,YAAA,GAANN,MAAM,CAAEW,IAAI,cAAAL,YAAA,eAAZA,YAAA,CAAcM,KAAK,IACnBZ,MAAM,aAANA,MAAM,gBAAAO,aAAA,GAANP,MAAM,CAAEW,IAAI,cAAAJ,aAAA,eAAZA,aAAA,CAAcM,WAAW,IACzBb,MAAM,aAANA,MAAM,gBAAAQ,aAAA,GAANR,MAAM,CAAEW,IAAI,cAAAH,aAAA,eAAZA,aAAA,CAAcM,eAAe,IAC7Bd,MAAM,aAANA,MAAM,gBAAAS,aAAA,GAANT,MAAM,CAAEW,IAAI,cAAAF,aAAA,eAAZA,aAAA,CAAcM,qBAAqB,CACpC;EACDnB,KAAK,CAAE,qBAAoB,EAAEc,iBAAiB,CAAC;EAE/C,IAAIA,iBAAiB,EAAE;IAAA,IAAAM,qBAAA,EAAAC,WAAA;IACrB;IACA;IACA,MAAMC,QAAQ,IAAAF,qBAAA,IAAAC,WAAA,GAAGlB,MAAM,CAACoB,GAAG,cAAAF,WAAA,uBAAVA,WAAA,CAAYG,kBAAkB,cAAAJ,qBAAA,cAAAA,qBAAA,GAAIjB,MAAM,CAACqB,kBAAkB;IAC5E;IACA,IAAIF,QAAQ,IAAIA,QAAQ,KAAK,WAAW,EAAE;MACxCG,kCAAiB,CAACC,aAAa,CAC7B,oBAAoB,EACpB,qJAAqJ,CACtJ;IACH;IACA;IACAjB,SAAS,CAACkB,oBAAoB,GAAG,WAAW;EAC9C,CAAC,MAAM;IACL;IACA;IACA;EAAA;EAGF,IAAIvB,MAAM,EAAE;IACV;IACAK,SAAS,CAACmB,sBAAsB,GAAG,cAAc;EACnD,CAAC,MAAM;IACL5B,KAAK,CAAE,kCAAiC,CAAC;IACzC,OAAOS,SAAS,CAACmB,sBAAsB;EACzC;EAEA,OAAOnB,SAAS;AAClB"}