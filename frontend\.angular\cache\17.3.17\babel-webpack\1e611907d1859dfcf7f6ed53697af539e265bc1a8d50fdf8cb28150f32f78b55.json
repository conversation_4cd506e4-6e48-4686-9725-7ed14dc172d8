{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Optional, Inject, Input, Output, ViewChild, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { MatRipple, _MatInternalFormField, MatCommonModule } from '@angular/material/core';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst _c0 = [\"input\"];\nconst _c1 = [\"label\"];\nconst _c2 = [\"*\"];\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-checkbox-default-options', {\n  providedIn: 'root',\n  factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate'\n  };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState = /*#__PURE__*/function (TransitionCheckState) {\n  /** The initial state of the component before any user interaction. */\n  TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n  /** The state representing the component when it's becoming checked. */\n  TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n  /** The state representing the component when it's becoming unchecked. */\n  TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n  /** The state representing the component when it's becoming indeterminate. */\n  TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n  return TransitionCheckState;\n}(TransitionCheckState || {});\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: /*#__PURE__*/forwardRef(() => MatCheckbox),\n  multi: true\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {}\n// Increasing integer for generating unique ids for checkbox components.\nlet nextUniqueId = 0;\n// Default checkbox configuration.\nconst defaults = /*#__PURE__*/MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nlet MatCheckbox = /*#__PURE__*/(() => {\n  class MatCheckbox {\n    /** Focuses the checkbox. */\n    focus() {\n      this._inputElement.nativeElement.focus();\n    }\n    /** Creates the change event that will be emitted by the checkbox. */\n    _createChangeEvent(isChecked) {\n      const event = new MatCheckboxChange();\n      event.source = this;\n      event.checked = isChecked;\n      return event;\n    }\n    /** Gets the element on which to add the animation CSS classes. */\n    _getAnimationTargetElement() {\n      return this._inputElement?.nativeElement;\n    }\n    /** Returns the unique id for the visual hidden input. */\n    get inputId() {\n      return `${this.id || this._uniqueId}-input`;\n    }\n    constructor(_elementRef, _changeDetectorRef, _ngZone, tabIndex, _animationMode, _options) {\n      this._elementRef = _elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._ngZone = _ngZone;\n      this._animationMode = _animationMode;\n      this._options = _options;\n      /** CSS classes to add when transitioning between the different checkbox states. */\n      this._animationClasses = {\n        uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n        uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n        checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n        checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n        indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n        indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked'\n      };\n      /**\n       * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n       * take precedence so this may be omitted.\n       */\n      this.ariaLabel = '';\n      /**\n       * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n       */\n      this.ariaLabelledby = null;\n      /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n      this.labelPosition = 'after';\n      /** Name value will be applied to the input element if present */\n      this.name = null;\n      /** Event emitted when the checkbox's `checked` value changes. */\n      this.change = new EventEmitter();\n      /** Event emitted when the checkbox's `indeterminate` value changes. */\n      this.indeterminateChange = new EventEmitter();\n      /**\n       * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n       * @docs-private\n       */\n      this._onTouched = () => {};\n      this._currentAnimationClass = '';\n      this._currentCheckState = TransitionCheckState.Init;\n      this._controlValueAccessorChangeFn = () => {};\n      this._validatorChangeFn = () => {};\n      this._checked = false;\n      this._disabled = false;\n      this._indeterminate = false;\n      this._options = this._options || defaults;\n      this.color = this._options.color || defaults.color;\n      this.tabIndex = parseInt(tabIndex) || 0;\n      this.id = this._uniqueId = `mat-mdc-checkbox-${++nextUniqueId}`;\n    }\n    ngOnChanges(changes) {\n      if (changes['required']) {\n        this._validatorChangeFn();\n      }\n    }\n    ngAfterViewInit() {\n      this._syncIndeterminate(this._indeterminate);\n    }\n    /** Whether the checkbox is checked. */\n    get checked() {\n      return this._checked;\n    }\n    set checked(value) {\n      if (value != this.checked) {\n        this._checked = value;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /** Whether the checkbox is disabled. */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      if (value !== this.disabled) {\n        this._disabled = value;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /**\n     * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n     * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n     * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n     * set to false.\n     */\n    get indeterminate() {\n      return this._indeterminate;\n    }\n    set indeterminate(value) {\n      const changed = value != this._indeterminate;\n      this._indeterminate = value;\n      if (changed) {\n        if (this._indeterminate) {\n          this._transitionCheckState(TransitionCheckState.Indeterminate);\n        } else {\n          this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n        }\n        this.indeterminateChange.emit(this._indeterminate);\n      }\n      this._syncIndeterminate(this._indeterminate);\n    }\n    _isRippleDisabled() {\n      return this.disableRipple || this.disabled;\n    }\n    /** Method being called whenever the label text changes. */\n    _onLabelTextChange() {\n      // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n      // component will be only marked for check, but no actual change detection runs automatically.\n      // Instead of going back into the zone in order to trigger a change detection which causes\n      // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n      // an explicit change detection for the checkbox view and its children.\n      this._changeDetectorRef.detectChanges();\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n      this.checked = !!value;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n      this._controlValueAccessorChangeFn = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n    }\n    // Implemented as a part of Validator.\n    validate(control) {\n      return this.required && control.value !== true ? {\n        'required': true\n      } : null;\n    }\n    // Implemented as a part of Validator.\n    registerOnValidatorChange(fn) {\n      this._validatorChangeFn = fn;\n    }\n    _transitionCheckState(newState) {\n      let oldState = this._currentCheckState;\n      let element = this._getAnimationTargetElement();\n      if (oldState === newState || !element) {\n        return;\n      }\n      if (this._currentAnimationClass) {\n        element.classList.remove(this._currentAnimationClass);\n      }\n      this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n      this._currentCheckState = newState;\n      if (this._currentAnimationClass.length > 0) {\n        element.classList.add(this._currentAnimationClass);\n        // Remove the animation class to avoid animation when the checkbox is moved between containers\n        const animationClass = this._currentAnimationClass;\n        this._ngZone.runOutsideAngular(() => {\n          setTimeout(() => {\n            element.classList.remove(animationClass);\n          }, 1000);\n        });\n      }\n    }\n    _emitChangeEvent() {\n      this._controlValueAccessorChangeFn(this.checked);\n      this.change.emit(this._createChangeEvent(this.checked));\n      // Assigning the value again here is redundant, but we have to do it in case it was\n      // changed inside the `change` listener which will cause the input to be out of sync.\n      if (this._inputElement) {\n        this._inputElement.nativeElement.checked = this.checked;\n      }\n    }\n    /** Toggles the `checked` state of the checkbox. */\n    toggle() {\n      this.checked = !this.checked;\n      this._controlValueAccessorChangeFn(this.checked);\n    }\n    _handleInputClick() {\n      const clickAction = this._options?.clickAction;\n      // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n      if (!this.disabled && clickAction !== 'noop') {\n        // When user manually click on the checkbox, `indeterminate` is set to false.\n        if (this.indeterminate && clickAction !== 'check') {\n          Promise.resolve().then(() => {\n            this._indeterminate = false;\n            this.indeterminateChange.emit(this._indeterminate);\n          });\n        }\n        this._checked = !this._checked;\n        this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n        // Emit our custom change event if the native input emitted one.\n        // It is important to only emit it, if the native input triggered one, because\n        // we don't want to trigger a change event, when the `checked` variable changes for example.\n        this._emitChangeEvent();\n      } else if (!this.disabled && clickAction === 'noop') {\n        // Reset native input when clicked with noop. The native checkbox becomes checked after\n        // click, reset it to be align with `checked` value of `mat-checkbox`.\n        this._inputElement.nativeElement.checked = this.checked;\n        this._inputElement.nativeElement.indeterminate = this.indeterminate;\n      }\n    }\n    _onInteractionEvent(event) {\n      // We always have to stop propagation on the change event.\n      // Otherwise the change event, from the input element, will bubble up and\n      // emit its event object to the `change` output.\n      event.stopPropagation();\n    }\n    _onBlur() {\n      // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n      // Angular does not expect events to be raised during change detection, so any state change\n      // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n      // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n      // telling the form control it has been touched until the next tick.\n      Promise.resolve().then(() => {\n        this._onTouched();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    _getAnimationClassForCheckStateTransition(oldState, newState) {\n      // Don't transition if animations are disabled.\n      if (this._animationMode === 'NoopAnimations') {\n        return '';\n      }\n      switch (oldState) {\n        case TransitionCheckState.Init:\n          // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n          // [checked] bound to it.\n          if (newState === TransitionCheckState.Checked) {\n            return this._animationClasses.uncheckedToChecked;\n          } else if (newState == TransitionCheckState.Indeterminate) {\n            return this._checked ? this._animationClasses.checkedToIndeterminate : this._animationClasses.uncheckedToIndeterminate;\n          }\n          break;\n        case TransitionCheckState.Unchecked:\n          return newState === TransitionCheckState.Checked ? this._animationClasses.uncheckedToChecked : this._animationClasses.uncheckedToIndeterminate;\n        case TransitionCheckState.Checked:\n          return newState === TransitionCheckState.Unchecked ? this._animationClasses.checkedToUnchecked : this._animationClasses.checkedToIndeterminate;\n        case TransitionCheckState.Indeterminate:\n          return newState === TransitionCheckState.Checked ? this._animationClasses.indeterminateToChecked : this._animationClasses.indeterminateToUnchecked;\n      }\n      return '';\n    }\n    /**\n     * Syncs the indeterminate value with the checkbox DOM node.\n     *\n     * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n     * property is supported on an element boils down to `if (propName in element)`. Domino's\n     * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n     * server-side rendering.\n     */\n    _syncIndeterminate(value) {\n      const nativeCheckbox = this._inputElement;\n      if (nativeCheckbox) {\n        nativeCheckbox.nativeElement.indeterminate = value;\n      }\n    }\n    _onInputClick() {\n      this._handleInputClick();\n    }\n    _onTouchTargetClick() {\n      this._handleInputClick();\n      if (!this.disabled) {\n        // Normally the input should be focused already, but if the click\n        // comes from the touch target, then we might have to focus it ourselves.\n        this._inputElement.nativeElement.focus();\n      }\n    }\n    /**\n     *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n     *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n     *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n     *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n     *  bubbles when the label is clicked.\n     */\n    _preventBubblingFromLabel(event) {\n      if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n        event.stopPropagation();\n      }\n    }\n    static {\n      this.ɵfac = function MatCheckbox_Factory(t) {\n        return new (t || MatCheckbox)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_CHECKBOX_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatCheckbox,\n        selectors: [[\"mat-checkbox\"]],\n        viewQuery: function MatCheckbox_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(MatRipple, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelElement = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ripple = _t.first);\n          }\n        },\n        hostAttrs: [1, \"mat-mdc-checkbox\"],\n        hostVars: 14,\n        hostBindings: function MatCheckbox_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"id\", ctx.id);\n            i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null);\n            i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"mat-accent\");\n            i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mdc-checkbox--disabled\", ctx.disabled)(\"mat-mdc-checkbox-disabled\", ctx.disabled)(\"mat-mdc-checkbox-checked\", ctx.checked);\n          }\n        },\n        inputs: {\n          ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n          ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n          ariaDescribedby: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"ariaDescribedby\"],\n          id: \"id\",\n          required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute],\n          labelPosition: \"labelPosition\",\n          name: \"name\",\n          value: \"value\",\n          disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n          tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? undefined : numberAttribute(value)],\n          color: \"color\",\n          checked: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"checked\", \"checked\", booleanAttribute],\n          disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n          indeterminate: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"indeterminate\", \"indeterminate\", booleanAttribute]\n        },\n        outputs: {\n          change: \"change\",\n          indeterminateChange: \"indeterminateChange\"\n        },\n        exportAs: [\"matCheckbox\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, {\n          provide: NG_VALIDATORS,\n          useExisting: MatCheckbox,\n          multi: true\n        }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c2,\n        decls: 15,\n        vars: 19,\n        consts: [[\"checkbox\", \"\"], [\"input\", \"\"], [\"label\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"click\", \"labelPosition\"], [1, \"mdc-checkbox\"], [1, \"mat-mdc-checkbox-touch-target\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"blur\", \"click\", \"change\", \"checked\", \"indeterminate\", \"disabled\", \"id\", \"required\", \"tabIndex\"], [1, \"mdc-checkbox__ripple\"], [1, \"mdc-checkbox__background\"], [\"focusable\", \"false\", \"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-checkbox-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-label\", 3, \"for\"]],\n        template: function MatCheckbox_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵprojectionDef();\n            i0.ɵɵelementStart(0, \"div\", 3);\n            i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._preventBubblingFromLabel($event));\n            });\n            i0.ɵɵelementStart(1, \"div\", 4, 0)(3, \"div\", 5);\n            i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_3_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._onTouchTargetClick());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"input\", 6, 1);\n            i0.ɵɵlistener(\"blur\", function MatCheckbox_Template_input_blur_4_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._onBlur());\n            })(\"click\", function MatCheckbox_Template_input_click_4_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._onInputClick());\n            })(\"change\", function MatCheckbox_Template_input_change_4_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx._onInteractionEvent($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(6, \"div\", 7);\n            i0.ɵɵelementStart(7, \"div\", 8);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(8, \"svg\", 9);\n            i0.ɵɵelement(9, \"path\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelement(10, \"div\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(11, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"label\", 13, 2);\n            i0.ɵɵprojection(14);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            const checkbox_r2 = i0.ɵɵreference(2);\n            i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"mdc-checkbox--selected\", ctx.checked);\n            i0.ɵɵproperty(\"checked\", ctx.checked)(\"indeterminate\", ctx.indeterminate)(\"disabled\", ctx.disabled)(\"id\", ctx.inputId)(\"required\", ctx.required)(\"tabIndex\", ctx.disabled ? -1 : ctx.tabIndex);\n            i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-checked\", ctx.indeterminate ? \"mixed\" : null)(\"name\", ctx.name)(\"value\", ctx.value);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"matRippleTrigger\", checkbox_r2)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"for\", ctx.inputId);\n          }\n        },\n        dependencies: [MatRipple, _MatInternalFormField],\n        styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color)}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatCheckbox;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: /*#__PURE__*/forwardRef(() => MatCheckboxRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nlet MatCheckboxRequiredValidator = /*#__PURE__*/(() => {\n  class MatCheckboxRequiredValidator extends CheckboxRequiredValidator {\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵMatCheckboxRequiredValidator_BaseFactory;\n        return function MatCheckboxRequiredValidator_Factory(t) {\n          return (ɵMatCheckboxRequiredValidator_BaseFactory || (ɵMatCheckboxRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MatCheckboxRequiredValidator)))(t || MatCheckboxRequiredValidator);\n        };\n      })();\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCheckboxRequiredValidator,\n        selectors: [[\"mat-checkbox\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"formControl\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"ngModel\", \"\"]],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatCheckboxRequiredValidator;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nlet _MatCheckboxRequiredValidatorModule = /*#__PURE__*/(() => {\n  class _MatCheckboxRequiredValidatorModule {\n    static {\n      this.ɵfac = function _MatCheckboxRequiredValidatorModule_Factory(t) {\n        return new (t || _MatCheckboxRequiredValidatorModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: _MatCheckboxRequiredValidatorModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n    }\n  }\n  return _MatCheckboxRequiredValidatorModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatCheckboxModule = /*#__PURE__*/(() => {\n  class MatCheckboxModule {\n    static {\n      this.ɵfac = function MatCheckboxModule_Factory(t) {\n        return new (t || MatCheckboxModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatCheckboxModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [MatCheckbox, MatCommonModule, MatCommonModule]\n      });\n    }\n  }\n  return MatCheckboxModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, TransitionCheckState, _MatCheckboxRequiredValidatorModule };\n//# sourceMappingURL=checkbox.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}