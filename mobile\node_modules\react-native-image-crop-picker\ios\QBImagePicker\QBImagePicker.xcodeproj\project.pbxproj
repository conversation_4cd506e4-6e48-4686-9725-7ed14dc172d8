// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		340A78CB1D7909FB00241672 /* Build QBImagePicker */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 340A78CE1D7909FC00241672 /* Build configuration list for PBXAggregateTarget "Build QBImagePicker" */;
			buildPhases = (
				340A78CF1D790A0100241672 /* ShellScript */,
			);
			dependencies = (
			);
			name = "Build QBImagePicker";
			productName = "Build QBImagePicker";
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		790A850F1AE7D4D9008E2A80 /* QBSlomoIconView.h in Headers */ = {isa = PBXBuildFile; fileRef = 790A850D1AE7D4D9008E2A80 /* QBSlomoIconView.h */; };
		790A85101AE7D4D9008E2A80 /* QBSlomoIconView.m in Sources */ = {isa = PBXBuildFile; fileRef = 790A850E1AE7D4D9008E2A80 /* QBSlomoIconView.m */; };
		AA3AD06F1ACF9A3A00BF523E /* QBVideoIconView.h in Headers */ = {isa = PBXBuildFile; fileRef = AA3AD06D1ACF9A3A00BF523E /* QBVideoIconView.h */; };
		AA3AD0701ACF9A3A00BF523E /* QBVideoIconView.m in Sources */ = {isa = PBXBuildFile; fileRef = AA3AD06E1ACF9A3A00BF523E /* QBVideoIconView.m */; };
		AA3AD0731ACFA06700BF523E /* QBVideoIndicatorView.h in Headers */ = {isa = PBXBuildFile; fileRef = AA3AD0711ACFA06700BF523E /* QBVideoIndicatorView.h */; };
		AA3AD0741ACFA06700BF523E /* QBVideoIndicatorView.m in Sources */ = {isa = PBXBuildFile; fileRef = AA3AD0721ACFA06700BF523E /* QBVideoIndicatorView.m */; };
		AAA8FE091ACDA079002A9710 /* QBImagePicker.h in Headers */ = {isa = PBXBuildFile; fileRef = AAA8FE081ACDA079002A9710 /* QBImagePicker.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAA8FE211ACDA090002A9710 /* QBImagePickerController.h in Headers */ = {isa = PBXBuildFile; fileRef = AAA8FE1F1ACDA090002A9710 /* QBImagePickerController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAA8FE221ACDA090002A9710 /* QBImagePickerController.m in Sources */ = {isa = PBXBuildFile; fileRef = AAA8FE201ACDA090002A9710 /* QBImagePickerController.m */; };
		AAA8FE5B1ACDA554002A9710 /* QBAlbumsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = AAA8FE591ACDA554002A9710 /* QBAlbumsViewController.h */; };
		AAA8FE5C1ACDA554002A9710 /* QBAlbumsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = AAA8FE5A1ACDA554002A9710 /* QBAlbumsViewController.m */; };
		AABBF0901ACEDDFF003188A3 /* QBCheckmarkView.h in Headers */ = {isa = PBXBuildFile; fileRef = AABBF08E1ACEDDFF003188A3 /* QBCheckmarkView.h */; };
		AABBF0911ACEDDFF003188A3 /* QBCheckmarkView.m in Sources */ = {isa = PBXBuildFile; fileRef = AABBF08F1ACEDDFF003188A3 /* QBCheckmarkView.m */; };
		AAF1CA1A1ACE5467005F6295 /* QBAlbumCell.h in Headers */ = {isa = PBXBuildFile; fileRef = AAF1CA181ACE5467005F6295 /* QBAlbumCell.h */; };
		AAF1CA1B1ACE5467005F6295 /* QBAlbumCell.m in Sources */ = {isa = PBXBuildFile; fileRef = AAF1CA191ACE5467005F6295 /* QBAlbumCell.m */; };
		AAF1CA231ACE5598005F6295 /* QBImagePicker.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AAF1CA221ACE5598005F6295 /* QBImagePicker.storyboard */; };
		AAF1CA2F1ACE6E46005F6295 /* QBAssetsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = AAF1CA2D1ACE6E46005F6295 /* QBAssetsViewController.h */; };
		AAF1CA301ACE6E46005F6295 /* QBAssetsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = AAF1CA2E1ACE6E46005F6295 /* QBAssetsViewController.m */; };
		AAF1CA331ACE6FB5005F6295 /* QBAssetCell.h in Headers */ = {isa = PBXBuildFile; fileRef = AAF1CA311ACE6FB5005F6295 /* QBAssetCell.h */; };
		AAF1CA341ACE6FB5005F6295 /* QBAssetCell.m in Sources */ = {isa = PBXBuildFile; fileRef = AAF1CA321ACE6FB5005F6295 /* QBAssetCell.m */; };
		E521B0A21B01D9300079461F /* QBImagePicker.strings in Resources */ = {isa = PBXBuildFile; fileRef = AAF1CA3F1ACE8918005F6295 /* QBImagePicker.strings */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		07A50A481B331A840010E048 /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		3A21B6F126614081003AF608 /* ro */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ro; path = ro.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		3A684B06251887B4006605D4 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		4237D94D25F8C071008C19B7 /* tr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = tr; path = tr.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		5A63116E200FC8C0006D76A5 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		6525CF3C27CFBCA8004F9AAE /* nb */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = nb; path = nb.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		74BB93661B2CE91A0044EB60 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		790A850D1AE7D4D9008E2A80 /* QBSlomoIconView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBSlomoIconView.h; sourceTree = "<group>"; };
		790A850E1AE7D4D9008E2A80 /* QBSlomoIconView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QBSlomoIconView.m; sourceTree = "<group>"; };
		7D92D893270E4FF700BFC37E /* uk */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = uk; path = uk.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		7D92D894270E500300BFC37E /* sv */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = sv; path = sv.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		82C8A8C927C370E300A3B0EB /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		1F83428028819165002788FE /* da */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = da; path = da.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		1F83427F28819162002788FE /* fi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fi; path = fi.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		AA3AD06D1ACF9A3A00BF523E /* QBVideoIconView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBVideoIconView.h; sourceTree = "<group>"; };
		AA3AD06E1ACF9A3A00BF523E /* QBVideoIconView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QBVideoIconView.m; sourceTree = "<group>"; };
		AA3AD0711ACFA06700BF523E /* QBVideoIndicatorView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBVideoIndicatorView.h; sourceTree = "<group>"; };
		AA3AD0721ACFA06700BF523E /* QBVideoIndicatorView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QBVideoIndicatorView.m; sourceTree = "<group>"; };
		AAA8FE031ACDA079002A9710 /* QBImagePicker.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = QBImagePicker.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AAA8FE071ACDA079002A9710 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		AAA8FE081ACDA079002A9710 /* QBImagePicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QBImagePicker.h; sourceTree = "<group>"; };
		AAA8FE1F1ACDA090002A9710 /* QBImagePickerController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBImagePickerController.h; sourceTree = "<group>"; };
		AAA8FE201ACDA090002A9710 /* QBImagePickerController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QBImagePickerController.m; sourceTree = "<group>"; };
		AAA8FE591ACDA554002A9710 /* QBAlbumsViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBAlbumsViewController.h; sourceTree = "<group>"; };
		AAA8FE5A1ACDA554002A9710 /* QBAlbumsViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QBAlbumsViewController.m; sourceTree = "<group>"; };
		AABBF08E1ACEDDFF003188A3 /* QBCheckmarkView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBCheckmarkView.h; sourceTree = "<group>"; };
		AABBF08F1ACEDDFF003188A3 /* QBCheckmarkView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QBCheckmarkView.m; sourceTree = "<group>"; };
		AAF1CA181ACE5467005F6295 /* QBAlbumCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBAlbumCell.h; sourceTree = "<group>"; };
		AAF1CA191ACE5467005F6295 /* QBAlbumCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QBAlbumCell.m; sourceTree = "<group>"; };
		AAF1CA221ACE5598005F6295 /* QBImagePicker.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = QBImagePicker.storyboard; sourceTree = "<group>"; };
		AAF1CA2D1ACE6E46005F6295 /* QBAssetsViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBAssetsViewController.h; sourceTree = "<group>"; };
		AAF1CA2E1ACE6E46005F6295 /* QBAssetsViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.objc; path = QBAssetsViewController.m; sourceTree = "<group>"; };
		AAF1CA311ACE6FB5005F6295 /* QBAssetCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QBAssetCell.h; sourceTree = "<group>"; };
		AAF1CA321ACE6FB5005F6295 /* QBAssetCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QBAssetCell.m; sourceTree = "<group>"; };
		AAF1CA3E1ACE8918005F6295 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		AAF1CA401ACE8A8B005F6295 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		D12C7DB1255B412500F13F68 /* it */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = it; path = it.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		D12C7DB2255B413200F13F68 /* pt */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = pt; path = pt.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		D12C7DB3255B413E00F13F68 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/QBImagePicker.strings"; sourceTree = "<group>"; };
		E521B0A11B01D6810079461F /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/QBImagePicker.strings"; sourceTree = "<group>"; };
		F167DB441C086AB90045AD5B /* pl */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = pl; path = pl.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
		FE931D551CEC597D00D343BD /* ru */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ru; path = ru.lproj/QBImagePicker.strings; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		AAA8FDFF1ACDA079002A9710 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		AA3AD0751ACFA22500BF523E /* Views */ = {
			isa = PBXGroup;
			children = (
				AAF1CA181ACE5467005F6295 /* QBAlbumCell.h */,
				AAF1CA191ACE5467005F6295 /* QBAlbumCell.m */,
				AAF1CA311ACE6FB5005F6295 /* QBAssetCell.h */,
				AAF1CA321ACE6FB5005F6295 /* QBAssetCell.m */,
				AABBF08E1ACEDDFF003188A3 /* QBCheckmarkView.h */,
				AABBF08F1ACEDDFF003188A3 /* QBCheckmarkView.m */,
				AA3AD06D1ACF9A3A00BF523E /* QBVideoIconView.h */,
				AA3AD06E1ACF9A3A00BF523E /* QBVideoIconView.m */,
				790A850D1AE7D4D9008E2A80 /* QBSlomoIconView.h */,
				790A850E1AE7D4D9008E2A80 /* QBSlomoIconView.m */,
				AA3AD0711ACFA06700BF523E /* QBVideoIndicatorView.h */,
				AA3AD0721ACFA06700BF523E /* QBVideoIndicatorView.m */,
			);
			name = Views;
			sourceTree = "<group>";
		};
		AA3AD0761ACFA23300BF523E /* ViewControllers */ = {
			isa = PBXGroup;
			children = (
				AAA8FE1F1ACDA090002A9710 /* QBImagePickerController.h */,
				AAA8FE201ACDA090002A9710 /* QBImagePickerController.m */,
				AAA8FE591ACDA554002A9710 /* QBAlbumsViewController.h */,
				AAA8FE5A1ACDA554002A9710 /* QBAlbumsViewController.m */,
				AAF1CA2D1ACE6E46005F6295 /* QBAssetsViewController.h */,
				AAF1CA2E1ACE6E46005F6295 /* QBAssetsViewController.m */,
			);
			name = ViewControllers;
			sourceTree = "<group>";
		};
		AAA8FDF91ACDA079002A9710 = {
			isa = PBXGroup;
			children = (
				AAA8FE051ACDA079002A9710 /* QBImagePicker */,
				AAA8FE041ACDA079002A9710 /* Products */,
			);
			sourceTree = "<group>";
		};
		AAA8FE041ACDA079002A9710 /* Products */ = {
			isa = PBXGroup;
			children = (
				AAA8FE031ACDA079002A9710 /* QBImagePicker.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AAA8FE051ACDA079002A9710 /* QBImagePicker */ = {
			isa = PBXGroup;
			children = (
				AAA8FE081ACDA079002A9710 /* QBImagePicker.h */,
				AA3AD0751ACFA22500BF523E /* Views */,
				AA3AD0761ACFA23300BF523E /* ViewControllers */,
				AAF1CA241ACE5665005F6295 /* Resources */,
				AAA8FE061ACDA079002A9710 /* Supporting Files */,
			);
			path = QBImagePicker;
			sourceTree = "<group>";
		};
		AAA8FE061ACDA079002A9710 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				AAA8FE071ACDA079002A9710 /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		AAF1CA241ACE5665005F6295 /* Resources */ = {
			isa = PBXGroup;
			children = (
				AAF1CA221ACE5598005F6295 /* QBImagePicker.storyboard */,
				AAF1CA3F1ACE8918005F6295 /* QBImagePicker.strings */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		AAA8FE001ACDA079002A9710 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				790A850F1AE7D4D9008E2A80 /* QBSlomoIconView.h in Headers */,
				AA3AD0731ACFA06700BF523E /* QBVideoIndicatorView.h in Headers */,
				AAF1CA331ACE6FB5005F6295 /* QBAssetCell.h in Headers */,
				AAF1CA2F1ACE6E46005F6295 /* QBAssetsViewController.h in Headers */,
				AAF1CA1A1ACE5467005F6295 /* QBAlbumCell.h in Headers */,
				AAA8FE5B1ACDA554002A9710 /* QBAlbumsViewController.h in Headers */,
				AA3AD06F1ACF9A3A00BF523E /* QBVideoIconView.h in Headers */,
				AAA8FE091ACDA079002A9710 /* QBImagePicker.h in Headers */,
				AABBF0901ACEDDFF003188A3 /* QBCheckmarkView.h in Headers */,
				AAA8FE211ACDA090002A9710 /* QBImagePickerController.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		AAA8FE021ACDA079002A9710 /* QBImagePicker */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AAA8FE191ACDA079002A9710 /* Build configuration list for PBXNativeTarget "QBImagePicker" */;
			buildPhases = (
				AAA8FDFE1ACDA079002A9710 /* Sources */,
				AAA8FDFF1ACDA079002A9710 /* Frameworks */,
				AAA8FE001ACDA079002A9710 /* Headers */,
				AAA8FE011ACDA079002A9710 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = QBImagePicker;
			productName = QBImagePicker;
			productReference = AAA8FE031ACDA079002A9710 /* QBImagePicker.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		AAA8FDFA1ACDA079002A9710 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0710;
				ORGANIZATIONNAME = "Katsuma Tanaka";
				TargetAttributes = {
					340A78CB1D7909FB00241672 = {
						CreatedOnToolsVersion = 7.3.1;
					};
					AAA8FE021ACDA079002A9710 = {
						CreatedOnToolsVersion = 6.2;
					};
				};
			};
			buildConfigurationList = AAA8FDFD1ACDA079002A9710 /* Build configuration list for PBXProject "QBImagePicker" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
				ja,
				"zh-Hans",
				es,
				de,
				pl,
				fr,
				ko,
				ru,
				it,
				pt,
				"zh-Hant",
				tr,
				ro,
				uk,
				sv,
				nb,
				vi,
				da,
				fi,
			);
			mainGroup = AAA8FDF91ACDA079002A9710;
			productRefGroup = AAA8FE041ACDA079002A9710 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				AAA8FE021ACDA079002A9710 /* QBImagePicker */,
				340A78CB1D7909FB00241672 /* Build QBImagePicker */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AAA8FE011ACDA079002A9710 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E521B0A21B01D9300079461F /* QBImagePicker.strings in Resources */,
				AAF1CA231ACE5598005F6295 /* QBImagePicker.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		340A78CF1D790A0100241672 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Merge Script\n\n# 1\n# Set bash script to exit immediately if any commands fail.\nset -e\n\n# 2\n# Setup some constants for use later on.\nFRAMEWORK_NAME=\"QBImagePicker\"\nDEST_DIR=\"$(cd ../$(SRCROOT) && pwd)/ImageCropPickerSDK\"\n\n# 3\n# If remnants from a previous build exist, delete them.\nif [ -d \"${SRCROOT}/build\" ]; then\nrm -rf \"${SRCROOT}/build\"\nfi\n\n# 4\n# Build the framework for device and for simulator (using\n# all needed architectures).\nxcodebuild -target \"${FRAMEWORK_NAME}\" -configuration Release -arch arm64 -arch armv7 -arch armv7s only_active_arch=no defines_module=yes -sdk \"iphoneos\"\nxcodebuild -target \"${FRAMEWORK_NAME}\" -configuration Release -arch x86_64 -arch i386 only_active_arch=no defines_module=yes -sdk \"iphonesimulator\"\n\n# 5\n# Remove .framework file if exists on Desktop from previous run.\nif [ -d \"$DEST_DIR/${FRAMEWORK_NAME}.framework\" ]; then\nrm -rf \"$DEST_DIR/${FRAMEWORK_NAME}.framework\"\nfi\n\n# 6\n# Copy the device version of framework to Desktop.\ncp -r \"${SRCROOT}/build/Release-iphoneos/${FRAMEWORK_NAME}.framework\" \"$DEST_DIR/${FRAMEWORK_NAME}.framework\"\n\n# 7\n# Replace the framework executable within the framework with\n# a new version created by merging the device and simulator\n# frameworks' executables with lipo.\nlipo -create -output \"$DEST_DIR/${FRAMEWORK_NAME}.framework/${FRAMEWORK_NAME}\" \"${SRCROOT}/build/Release-iphoneos/${FRAMEWORK_NAME}.framework/${FRAMEWORK_NAME}\" \"${SRCROOT}/build/Release-iphonesimulator/${FRAMEWORK_NAME}.framework/${FRAMEWORK_NAME}\"\n\n# 9\n# Delete the most recent build.\nif [ -d \"${SRCROOT}/build\" ]; then\nrm -rf \"${SRCROOT}/build\"\nfi";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		AAA8FDFE1ACDA079002A9710 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AAF1CA341ACE6FB5005F6295 /* QBAssetCell.m in Sources */,
				AA3AD0701ACF9A3A00BF523E /* QBVideoIconView.m in Sources */,
				790A85101AE7D4D9008E2A80 /* QBSlomoIconView.m in Sources */,
				AABBF0911ACEDDFF003188A3 /* QBCheckmarkView.m in Sources */,
				AAF1CA301ACE6E46005F6295 /* QBAssetsViewController.m in Sources */,
				AAF1CA1B1ACE5467005F6295 /* QBAlbumCell.m in Sources */,
				AAA8FE221ACDA090002A9710 /* QBImagePickerController.m in Sources */,
				AA3AD0741ACFA06700BF523E /* QBVideoIndicatorView.m in Sources */,
				AAA8FE5C1ACDA554002A9710 /* QBAlbumsViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		AAF1CA3F1ACE8918005F6295 /* QBImagePicker.strings */ = {
			isa = PBXVariantGroup;
			children = (
				AAF1CA3E1ACE8918005F6295 /* en */,
				AAF1CA401ACE8A8B005F6295 /* ja */,
				E521B0A11B01D6810079461F /* zh-Hans */,
				07A50A481B331A840010E048 /* es */,
				74BB93661B2CE91A0044EB60 /* de */,
				F167DB441C086AB90045AD5B /* pl */,
				5A63116E200FC8C0006D76A5 /* fr */,
				3A684B06251887B4006605D4 /* ko */,
				FE931D551CEC597D00D343BD /* ru */,
				D12C7DB1255B412500F13F68 /* it */,
				D12C7DB2255B413200F13F68 /* pt */,
				D12C7DB3255B413E00F13F68 /* zh-Hant */,
				4237D94D25F8C071008C19B7 /* tr */,
				3A21B6F126614081003AF608 /* ro */,
				7D92D893270E4FF700BFC37E /* uk */,
				7D92D894270E500300BFC37E /* sv */,
				6525CF3C27CFBCA8004F9AAE /* nb */,
				82C8A8C927C370E300A3B0EB /* vi */,
				1F83428028819165002788FE /* da */,
				1F83427F28819162002788FE /* fi */,
			);
			name = QBImagePicker.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		340A78CC1D7909FC00241672 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		340A78CD1D7909FC00241672 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		AAA8FE171ACDA079002A9710 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PROVISIONING_PROFILE = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		AAA8FE181ACDA079002A9710 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				PROVISIONING_PROFILE = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		AAA8FE1A1ACDA079002A9710 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = QBImagePicker/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "jp.questbeat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				VALID_ARCHS = "arm64 armv7 armv7s i386 x86_64";
			};
			name = Debug;
		};
		AAA8FE1B1ACDA079002A9710 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = QBImagePicker/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "jp.questbeat.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				VALID_ARCHS = "arm64 armv7 armv7s i386 x86_64";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		340A78CE1D7909FC00241672 /* Build configuration list for PBXAggregateTarget "Build QBImagePicker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				340A78CC1D7909FC00241672 /* Debug */,
				340A78CD1D7909FC00241672 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AAA8FDFD1ACDA079002A9710 /* Build configuration list for PBXProject "QBImagePicker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AAA8FE171ACDA079002A9710 /* Debug */,
				AAA8FE181ACDA079002A9710 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AAA8FE191ACDA079002A9710 /* Build configuration list for PBXNativeTarget "QBImagePicker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AAA8FE1A1ACDA079002A9710 /* Debug */,
				AAA8FE1B1ACDA079002A9710 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = AAA8FDFA1ACDA079002A9710 /* Project object */;
}
