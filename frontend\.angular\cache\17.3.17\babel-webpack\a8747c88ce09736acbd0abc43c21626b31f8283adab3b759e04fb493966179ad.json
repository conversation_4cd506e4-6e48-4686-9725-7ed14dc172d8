{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, Input, Directive, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"], [\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"], [\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"], [\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"], [\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]], \"*\"];\nconst _c2 = [\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\", \"*\"];\nconst _c3 = [[[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]], [[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], \"*\"];\nconst _c4 = [\"[mat-card-avatar], [matCardAvatar]\", \"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"*\"];\nconst MAT_CARD_CONFIG = /*#__PURE__*/new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nlet MatCard = /*#__PURE__*/(() => {\n  class MatCard {\n    constructor(config) {\n      this.appearance = config?.appearance || 'raised';\n    }\n    static {\n      this.ɵfac = function MatCard_Factory(t) {\n        return new (t || MatCard)(i0.ɵɵdirectiveInject(MAT_CARD_CONFIG, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatCard,\n        selectors: [[\"mat-card\"]],\n        hostAttrs: [1, \"mat-mdc-card\", \"mdc-card\"],\n        hostVars: 4,\n        hostBindings: function MatCard_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mat-mdc-card-outlined\", ctx.appearance === \"outlined\")(\"mdc-card--outlined\", ctx.appearance === \"outlined\");\n          }\n        },\n        inputs: {\n          appearance: \"appearance\"\n        },\n        exportAs: [\"matCard\"],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 1,\n        vars: 0,\n        template: function MatCard_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵprojection(0);\n          }\n        },\n        styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatCard;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nlet MatCardTitle = /*#__PURE__*/(() => {\n  class MatCardTitle {\n    static {\n      this.ɵfac = function MatCardTitle_Factory(t) {\n        return new (t || MatCardTitle)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardTitle,\n        selectors: [[\"mat-card-title\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"matCardTitle\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-card-title\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardTitle;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nlet MatCardTitleGroup = /*#__PURE__*/(() => {\n  class MatCardTitleGroup {\n    static {\n      this.ɵfac = function MatCardTitleGroup_Factory(t) {\n        return new (t || MatCardTitleGroup)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatCardTitleGroup,\n        selectors: [[\"mat-card-title-group\"]],\n        hostAttrs: [1, \"mat-mdc-card-title-group\"],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c2,\n        decls: 4,\n        vars: 0,\n        template: function MatCardTitleGroup_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c1);\n            i0.ɵɵelementStart(0, \"div\");\n            i0.ɵɵprojection(1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(2, 1);\n            i0.ɵɵprojection(3, 2);\n          }\n        },\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatCardTitleGroup;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nlet MatCardContent = /*#__PURE__*/(() => {\n  class MatCardContent {\n    static {\n      this.ɵfac = function MatCardContent_Factory(t) {\n        return new (t || MatCardContent)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardContent,\n        selectors: [[\"mat-card-content\"]],\n        hostAttrs: [1, \"mat-mdc-card-content\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nlet MatCardSubtitle = /*#__PURE__*/(() => {\n  class MatCardSubtitle {\n    static {\n      this.ɵfac = function MatCardSubtitle_Factory(t) {\n        return new (t || MatCardSubtitle)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardSubtitle,\n        selectors: [[\"mat-card-subtitle\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-card-subtitle\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardSubtitle;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nlet MatCardActions = /*#__PURE__*/(() => {\n  class MatCardActions {\n    constructor() {\n      // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n      // as to not conflict with the native `align` attribute.\n      /** Position of the actions inside the card. */\n      this.align = 'start';\n    }\n    static {\n      this.ɵfac = function MatCardActions_Factory(t) {\n        return new (t || MatCardActions)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardActions,\n        selectors: [[\"mat-card-actions\"]],\n        hostAttrs: [1, \"mat-mdc-card-actions\", \"mdc-card__actions\"],\n        hostVars: 2,\n        hostBindings: function MatCardActions_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"mat-mdc-card-actions-align-end\", ctx.align === \"end\");\n          }\n        },\n        inputs: {\n          align: \"align\"\n        },\n        exportAs: [\"matCardActions\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardActions;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nlet MatCardHeader = /*#__PURE__*/(() => {\n  class MatCardHeader {\n    static {\n      this.ɵfac = function MatCardHeader_Factory(t) {\n        return new (t || MatCardHeader)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatCardHeader,\n        selectors: [[\"mat-card-header\"]],\n        hostAttrs: [1, \"mat-mdc-card-header\"],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c4,\n        decls: 4,\n        vars: 0,\n        consts: [[1, \"mat-mdc-card-header-text\"]],\n        template: function MatCardHeader_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef(_c3);\n            i0.ɵɵprojection(0);\n            i0.ɵɵelementStart(1, \"div\", 0);\n            i0.ɵɵprojection(2, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵprojection(3, 2);\n          }\n        },\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatCardHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nlet MatCardFooter = /*#__PURE__*/(() => {\n  class MatCardFooter {\n    static {\n      this.ɵfac = function MatCardFooter_Factory(t) {\n        return new (t || MatCardFooter)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardFooter,\n        selectors: [[\"mat-card-footer\"]],\n        hostAttrs: [1, \"mat-mdc-card-footer\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardFooter;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nlet MatCardImage = /*#__PURE__*/(() => {\n  class MatCardImage {\n    static {\n      this.ɵfac = function MatCardImage_Factory(t) {\n        return new (t || MatCardImage)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardImage,\n        selectors: [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-card-image\", \"mdc-card__media\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardImage;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Same as `MatCardImage`, but small. */\nlet MatCardSmImage = /*#__PURE__*/(() => {\n  class MatCardSmImage {\n    static {\n      this.ɵfac = function MatCardSmImage_Factory(t) {\n        return new (t || MatCardSmImage)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardSmImage,\n        selectors: [[\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-card-sm-image\", \"mdc-card__media\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardSmImage;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Same as `MatCardImage`, but medium. */\nlet MatCardMdImage = /*#__PURE__*/(() => {\n  class MatCardMdImage {\n    static {\n      this.ɵfac = function MatCardMdImage_Factory(t) {\n        return new (t || MatCardMdImage)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardMdImage,\n        selectors: [[\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-card-md-image\", \"mdc-card__media\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardMdImage;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Same as `MatCardImage`, but large. */\nlet MatCardLgImage = /*#__PURE__*/(() => {\n  class MatCardLgImage {\n    static {\n      this.ɵfac = function MatCardLgImage_Factory(t) {\n        return new (t || MatCardLgImage)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardLgImage,\n        selectors: [[\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-card-lg-image\", \"mdc-card__media\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardLgImage;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Same as `MatCardImage`, but extra-large. */\nlet MatCardXlImage = /*#__PURE__*/(() => {\n  class MatCardXlImage {\n    static {\n      this.ɵfac = function MatCardXlImage_Factory(t) {\n        return new (t || MatCardXlImage)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardXlImage,\n        selectors: [[\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-card-xl-image\", \"mdc-card__media\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardXlImage;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nlet MatCardAvatar = /*#__PURE__*/(() => {\n  class MatCardAvatar {\n    static {\n      this.ɵfac = function MatCardAvatar_Factory(t) {\n        return new (t || MatCardAvatar)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatCardAvatar,\n        selectors: [[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-card-avatar\"],\n        standalone: true\n      });\n    }\n  }\n  return MatCardAvatar;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst CARD_DIRECTIVES = [MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage];\nlet MatCardModule = /*#__PURE__*/(() => {\n  class MatCardModule {\n    static {\n      this.ɵfac = function MatCardModule_Factory(t) {\n        return new (t || MatCardModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatCardModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [MatCommonModule, CommonModule, MatCommonModule]\n      });\n    }\n  }\n  return MatCardModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };\n//# sourceMappingURL=card.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}