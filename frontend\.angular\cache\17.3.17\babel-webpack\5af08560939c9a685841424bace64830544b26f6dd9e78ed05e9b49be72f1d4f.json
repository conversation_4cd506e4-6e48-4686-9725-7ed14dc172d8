{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable } from '@angular/core';\nimport { Subject, Observable } from 'rxjs';\nimport { filter, shareReplay, takeUntil } from 'rxjs/operators';\n\n/**\n * <PERSON><PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = e => {\n  if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n    console.error(`${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`);\n  }\n};\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n  constructor(/** The box type to observe for resizes. */\n  _box) {\n    this._box = _box;\n    /** Stream that emits when the shared observer is destroyed. */\n    this._destroyed = new Subject();\n    /** Stream of all events from the ResizeObserver. */\n    this._resizeSubject = new Subject();\n    /** A map of elements to streams of their resize events. */\n    this._elementObservables = new Map();\n    if (typeof ResizeObserver !== 'undefined') {\n      this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n    }\n  }\n  /**\n   * Gets a stream of resize events for the given element.\n   * @param target The element to observe.\n   * @return The stream of resize events for the element.\n   */\n  observe(target) {\n    if (!this._elementObservables.has(target)) {\n      this._elementObservables.set(target, new Observable(observer => {\n        const subscription = this._resizeSubject.subscribe(observer);\n        this._resizeObserver?.observe(target, {\n          box: this._box\n        });\n        return () => {\n          this._resizeObserver?.unobserve(target);\n          subscription.unsubscribe();\n          this._elementObservables.delete(target);\n        };\n      }).pipe(filter(entries => entries.some(entry => entry.target === target)),\n      // Share a replay of the last event so that subsequent calls to observe the same element\n      // receive initial sizing info like the first one. Also enable ref counting so the\n      // element will be automatically unobserved when there are no more subscriptions.\n      shareReplay({\n        bufferSize: 1,\n        refCount: true\n      }), takeUntil(this._destroyed)));\n    }\n    return this._elementObservables.get(target);\n  }\n  /** Destroys this instance. */\n  destroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._resizeSubject.complete();\n    this._elementObservables.clear();\n  }\n}\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\nlet SharedResizeObserver = /*#__PURE__*/(() => {\n  class SharedResizeObserver {\n    constructor() {\n      /** Map of box type to shared resize observer. */\n      this._observers = new Map();\n      /** The Angular zone. */\n      this._ngZone = inject(NgZone);\n      if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        this._ngZone.runOutsideAngular(() => {\n          window.addEventListener('error', loopLimitExceededErrorHandler);\n        });\n      }\n    }\n    ngOnDestroy() {\n      for (const [, observer] of this._observers) {\n        observer.destroy();\n      }\n      this._observers.clear();\n      if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        window.removeEventListener('error', loopLimitExceededErrorHandler);\n      }\n    }\n    /**\n     * Gets a stream of resize events for the given target element and box type.\n     * @param target The element to observe for resizes.\n     * @param options Options to pass to the `ResizeObserver`\n     * @return The stream of resize events for the element.\n     */\n    observe(target, options) {\n      const box = options?.box || 'content-box';\n      if (!this._observers.has(box)) {\n        this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n      }\n      return this._observers.get(box).observe(target);\n    }\n    static {\n      this.ɵfac = function SharedResizeObserver_Factory(t) {\n        return new (t || SharedResizeObserver)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: SharedResizeObserver,\n        factory: SharedResizeObserver.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SharedResizeObserver;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SharedResizeObserver };\n//# sourceMappingURL=private.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}