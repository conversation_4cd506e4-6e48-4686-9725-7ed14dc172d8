{"ast": null, "code": "import { RouterOutlet } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/toolbar\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(router) {\n      this.router = router;\n      this.title = 'E-Commerce Admin Dashboard';\n    }\n    ngOnInit() {\n      console.log('App component initialized');\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 9,\n        vars: 1,\n        consts: [[\"color\", \"primary\"], [1, \"spacer\"], [\"mat-icon-button\", \"\"], [1, \"app-container\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"mat-toolbar\", 0)(1, \"span\");\n            i0.ɵɵtext(2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(3, \"span\", 1);\n            i0.ɵɵelementStart(4, \"button\", 2)(5, \"mat-icon\");\n            i0.ɵɵtext(6, \"settings\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 3);\n            i0.ɵɵelement(8, \"router-outlet\");\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.title);\n          }\n        },\n        dependencies: [CommonModule, RouterOutlet, MatToolbarModule, i2.MatToolbar, MatButtonModule, i3.MatIconButton, MatIconModule, i4.MatIcon, MatProgressSpinnerModule],\n        styles: [\".app-container[_ngcontent-%COMP%]{min-height:calc(100vh - 64px);background-color:#f5f5f5;padding:20px}.spacer[_ngcontent-%COMP%]{flex:1 1 auto}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}