"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.OUTLINE_MINIMIZED_LABEL_Y_OFFSET = exports.MIN_WIDTH = exports.MIN_DENSE_HEIGHT_WL = exports.MIN_DENSE_HEIGHT_OUTLINED = exports.MIN_DENSE_HEIGHT = exports.MINIMIZED_LABEL_Y_OFFSET = exports.MINIMIZED_LABEL_FONT_SIZE = exports.MD3_OUTLINED_INPUT_OFFSET = exports.MD3_MIN_HEIGHT = exports.MD3_LABEL_PADDING_TOP = exports.MD3_LABEL_PADDING_HORIZONTAL = exports.MD3_INPUT_PADDING_HORIZONTAL = exports.MD3_ICON_OFFSET = exports.MD3_FLAT_INPUT_OFFSET = exports.MD3_AFFIX_OFFSET = exports.MD3_ADORNMENT_OFFSET = exports.MD2_OUTLINED_INPUT_OFFSET = exports.MD2_MIN_HEIGHT = exports.MD2_LABEL_PADDING_TOP = exports.MD2_LABEL_PADDING_HORIZONTAL = exports.MD2_INPUT_PADDING_HORIZONTAL = exports.MD2_ICON_OFFSET = exports.MD2_FLAT_INPUT_OFFSET = exports.MD2_AFFIX_OFFSET = exports.MD2_ADORNMENT_OFFSET = exports.MAXIMIZED_LABEL_FONT_SIZE = exports.LABEL_WIGGLE_X_OFFSET = exports.LABEL_PADDING_TOP_DENSE = exports.LABEL_PADDING_TOP = exports.ICON_SIZE = exports.ADORNMENT_SIZE = void 0;
const MAXIMIZED_LABEL_FONT_SIZE = exports.MAXIMIZED_LABEL_FONT_SIZE = 16;
const MINIMIZED_LABEL_FONT_SIZE = exports.MINIMIZED_LABEL_FONT_SIZE = 12;
const LABEL_WIGGLE_X_OFFSET = exports.LABEL_WIGGLE_X_OFFSET = 4;
const ADORNMENT_SIZE = exports.ADORNMENT_SIZE = 24;
const MIN_WIDTH = exports.MIN_WIDTH = 100;

//Text input affix offset
const MD2_AFFIX_OFFSET = exports.MD2_AFFIX_OFFSET = 12;
const MD3_AFFIX_OFFSET = exports.MD3_AFFIX_OFFSET = 16;

// Text input icon
const ICON_SIZE = exports.ICON_SIZE = 24;
const MD2_ICON_OFFSET = exports.MD2_ICON_OFFSET = 12;
const MD3_ICON_OFFSET = exports.MD3_ICON_OFFSET = 16;

// Text input common
const MD2_MIN_HEIGHT = exports.MD2_MIN_HEIGHT = 64;
const MD3_MIN_HEIGHT = exports.MD3_MIN_HEIGHT = 56;
const MD3_ADORNMENT_OFFSET = exports.MD3_ADORNMENT_OFFSET = 16;
const MD2_ADORNMENT_OFFSET = exports.MD2_ADORNMENT_OFFSET = 12;
const LABEL_PADDING_TOP_DENSE = exports.LABEL_PADDING_TOP_DENSE = 24;
const LABEL_PADDING_TOP = exports.LABEL_PADDING_TOP = 8;

// Text input flat
const MD2_LABEL_PADDING_TOP = exports.MD2_LABEL_PADDING_TOP = 30;
const MD3_LABEL_PADDING_TOP = exports.MD3_LABEL_PADDING_TOP = 26;
const MD2_LABEL_PADDING_HORIZONTAL = exports.MD2_LABEL_PADDING_HORIZONTAL = 12;
const MD3_LABEL_PADDING_HORIZONTAL = exports.MD3_LABEL_PADDING_HORIZONTAL = 16;
const MD2_FLAT_INPUT_OFFSET = exports.MD2_FLAT_INPUT_OFFSET = 8;
const MD3_FLAT_INPUT_OFFSET = exports.MD3_FLAT_INPUT_OFFSET = 16;
const MINIMIZED_LABEL_Y_OFFSET = exports.MINIMIZED_LABEL_Y_OFFSET = -18;
const MIN_DENSE_HEIGHT_WL = exports.MIN_DENSE_HEIGHT_WL = 52;
const MIN_DENSE_HEIGHT = exports.MIN_DENSE_HEIGHT = 40;

// Text input outlined
const MD2_INPUT_PADDING_HORIZONTAL = exports.MD2_INPUT_PADDING_HORIZONTAL = 14;
const MD3_INPUT_PADDING_HORIZONTAL = exports.MD3_INPUT_PADDING_HORIZONTAL = 16;

// extra space to avoid overlapping input's text and icon
const MD2_OUTLINED_INPUT_OFFSET = exports.MD2_OUTLINED_INPUT_OFFSET = 8;
const MD3_OUTLINED_INPUT_OFFSET = exports.MD3_OUTLINED_INPUT_OFFSET = 16;
const OUTLINE_MINIMIZED_LABEL_Y_OFFSET = exports.OUTLINE_MINIMIZED_LABEL_Y_OFFSET = -6;
const MIN_DENSE_HEIGHT_OUTLINED = exports.MIN_DENSE_HEIGHT_OUTLINED = 48;
//# sourceMappingURL=constants.js.map