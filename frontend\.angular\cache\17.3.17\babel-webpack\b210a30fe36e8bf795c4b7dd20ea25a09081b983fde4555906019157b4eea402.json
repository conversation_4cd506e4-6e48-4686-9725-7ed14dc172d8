{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nlet LoginComponent = class LoginComponent {\n  constructor(formBuilder, router, route) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.route = route;\n    this.hidePassword = true;\n    this.returnUrl = '/dashboard';\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      rememberMe: [false]\n    });\n  }\n  ngOnInit() {\n    // Get return url from route parameters or default to '/dashboard'\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      console.log('Login attempt with:', this.loginForm.value);\n      // Simulate login process\n      setTimeout(() => {\n        console.log('Login successful');\n        this.router.navigate([this.returnUrl]);\n      }, 1000);\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getErrorMessage(fieldName) {\n    const control = this.loginForm.get(fieldName);\n    if (control?.hasError('required')) {\n      return `${this.getFieldDisplayName(fieldName)} is required`;\n    }\n    if (control?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control?.hasError('minlength')) {\n      const requiredLength = control.errors?.['minlength']?.requiredLength;\n      return `Password must be at least ${requiredLength} characters long`;\n    }\n    return '';\n  }\n  getFieldDisplayName(fieldName) {\n    const fieldNames = {\n      email: 'Email',\n      password: 'Password'\n    };\n    return fieldNames[fieldName] || fieldName;\n  }\n  togglePasswordVisibility() {\n    this.hidePassword = !this.hidePassword;\n  }\n  navigateToRegister() {\n    this.router.navigate(['/auth/register']);\n  }\n  navigateToForgotPassword() {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n};\nLoginComponent = __decorate([Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatCheckboxModule],\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n})], LoginComponent);\nexport { LoginComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}