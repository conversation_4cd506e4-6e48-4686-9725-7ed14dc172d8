{"version": 3, "names": ["width", "height", "Dimensions", "get", "initialMetrics", "Platform", "OS", "initialWindowMetrics", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "children", "style", "React", "useContext", "SafeAreaInsetsContext", "styles", "container", "SafeAreaFrameProvider", "element", "useRef", "set<PERSON>rame", "useState", "useEffect", "current", "rect", "getBoundingClientRect", "timeout", "observer", "ResizeObserver", "entries", "entry", "contentRect", "clearTimeout", "setTimeout", "observe", "disconnect", "StyleSheet", "absoluteFillObject", "pointerEvents", "visibility", "create", "flex"], "sourceRoot": "../../src", "sources": ["SafeAreaProviderCompat.tsx"], "mappings": ";;;;;;AAAA;AACA;AAQA;AAMwC;AAAA;AAOxC,MAAM;EAAEA,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG;AAAE,CAAC,GAAGC,uBAAU,CAACC,GAAG,CAAC,QAAQ,CAAC;;AAE1D;AACA;AACA;AACA,MAAMC,cAAc,GAClBC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIC,gDAAoB,IAAI,IAAI,GACjD;EACEC,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEV,KAAK;IAAEC;EAAO,CAAC;EACpCU,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDR,gDAAoB;AAEX,SAASS,sBAAsB,OAA6B;EAAA,IAA5B;IAAEC,QAAQ;IAAEC;EAAa,CAAC;EACvE,MAAMP,MAAM,GAAGQ,KAAK,CAACC,UAAU,CAACC,iDAAqB,CAAC;EAEtD,IAAIV,MAAM,EAAE;IACV;IACA;IACA;IACA,oBAAO,oBAAC,iBAAI;MAAC,KAAK,EAAE,CAACW,MAAM,CAACC,SAAS,EAAEL,KAAK;IAAE,GAAED,QAAQ,CAAQ;EAClE;EAEA,IAAIZ,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzBW,QAAQ,gBACN,oBAAC,qBAAqB;MAAC,cAAc,EAAEb;IAAe,GACnDa,QAAQ,CAEZ;EACH;EAEA,oBACE,oBAAC,4CAAgB;IAAC,cAAc,EAAEb,cAAe;IAAC,KAAK,EAAEc;EAAM,GAC5DD,QAAQ,CACQ;AAEvB;;AAEA;AACA;AACA,MAAMO,qBAAqB,GAAG,SAMxB;EAAA,IANyB;IAC7BpB,cAAc;IACda;EAIF,CAAC;EACC,MAAMQ,OAAO,GAAGN,KAAK,CAACO,MAAM,CAAiB,IAAI,CAAC;EAClD,MAAM,CAAClB,KAAK,EAAEmB,QAAQ,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAACxB,cAAc,CAACI,KAAK,CAAC;EAE9DW,KAAK,CAACU,SAAS,CAAC,MAAM;IACpB,IAAIJ,OAAO,CAACK,OAAO,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMC,IAAI,GAAGN,OAAO,CAACK,OAAO,CAACE,qBAAqB,EAAE;IAEpDL,QAAQ,CAAC;MACPlB,CAAC,EAAEsB,IAAI,CAACtB,CAAC;MACTC,CAAC,EAAEqB,IAAI,CAACrB,CAAC;MACTV,KAAK,EAAE+B,IAAI,CAAC/B,KAAK;MACjBC,MAAM,EAAE8B,IAAI,CAAC9B;IACf,CAAC,CAAC;IAEF,IAAIgC,OAAuB;IAE3B,MAAMC,QAAQ,GAAG,IAAIC,cAAc,CAAEC,OAAO,IAAK;MAC/C,MAAMC,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC;MAExB,IAAIC,KAAK,EAAE;QACT,MAAM;UAAE5B,CAAC;UAAEC,CAAC;UAAEV,KAAK;UAAEC;QAAO,CAAC,GAAGoC,KAAK,CAACC,WAAW;;QAEjD;QACAC,YAAY,CAACN,OAAO,CAAC;QACrBA,OAAO,GAAGO,UAAU,CAAC,MAAM;UACzBb,QAAQ,CAAC;YAAElB,CAAC;YAAEC,CAAC;YAAEV,KAAK;YAAEC;UAAO,CAAC,CAAC;QACnC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,CAAC;IAEFiC,QAAQ,CAACO,OAAO,CAAChB,OAAO,CAACK,OAAO,CAAC;IAEjC,OAAO,MAAM;MACXI,QAAQ,CAACQ,UAAU,EAAE;MACrBH,YAAY,CAACN,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE,oBAAC,gDAAoB,CAAC,QAAQ;IAAC,KAAK,EAAEzB;EAAM,gBAC1C;IACE,GAAG,EAAEiB,OAAQ;IACb,KAAK,EAAE;MACL,GAAGkB,uBAAU,CAACC,kBAAkB;MAChCC,aAAa,EAAE,MAAM;MACrBC,UAAU,EAAE;IACd;EAAE,EACF,EACD7B,QAAQ,CACqB;AAEpC,CAAC;AAEDD,sBAAsB,CAACZ,cAAc,GAAGA,cAAc;AAEtD,MAAMkB,MAAM,GAAGqB,uBAAU,CAACI,MAAM,CAAC;EAC/BxB,SAAS,EAAE;IACTyB,IAAI,EAAE;EACR;AACF,CAAC,CAAC"}