/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { createFakeEvent, createKeyboardEvent, createMouseEvent, createPointerEvent, createTouchEvent, } from './event-objects';
/**
 * Utility to dispatch any event on a Node.
 * @docs-private
 */
export function dispatchEvent(node, event) {
    node.dispatchEvent(event);
    return event;
}
/**
 * Shorthand to dispatch a fake event on a specified node.
 * @docs-private
 */
export function dispatchFakeEvent(node, type, bubbles) {
    return dispatchEvent(node, createFakeEvent(type, bubbles));
}
/**
 * Shorthand to dispatch a keyboard event with a specified key code and
 * optional modifiers.
 * @docs-private
 */
export function dispatchKeyboardEvent(node, type, keyCode, key, modifiers) {
    return dispatchEvent(node, createKeyboardEvent(type, keyCode, key, modifiers));
}
/**
 * Shorthand to dispatch a mouse event on the specified coordinates.
 * @docs-private
 */
export function dispatchMouseEvent(node, type, clientX = 0, clientY = 0, offsetX, offsetY, button, modifiers) {
    return dispatchEvent(node, createMouseEvent(type, clientX, clientY, offsetX, offsetY, button, modifiers));
}
/**
 * Shorthand to dispatch a pointer event on the specified coordinates.
 * @docs-private
 */
export function dispatchPointerEvent(node, type, clientX = 0, clientY = 0, offsetX, offsetY, options) {
    return dispatchEvent(node, createPointerEvent(type, clientX, clientY, offsetX, offsetY, options));
}
/**
 * Shorthand to dispatch a touch event on the specified coordinates.
 * @docs-private
 */
export function dispatchTouchEvent(node, type, pageX = 0, pageY = 0, clientX = 0, clientY = 0) {
    return dispatchEvent(node, createTouchEvent(type, pageX, pageY, clientX, clientY));
}
//# sourceMappingURL=data:application/json;base64,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