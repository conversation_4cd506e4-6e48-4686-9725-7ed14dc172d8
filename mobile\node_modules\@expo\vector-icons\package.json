{"name": "@expo/vector-icons", "sideEffects": false, "version": "13.0.0", "description": "Built-in support for popular icon fonts and the tooling to create your own Icon components from your font and glyph map. This is a wrapper around react-native-vector-icons to make it compatible with Expo.", "main": "build/IconsLazy.js", "module": "build/Icons.js", "types": "build/Icons.d.ts", "scripts": {"copy-vendor": "cp -R src/vendor/ build/vendor/", "generate-lazy": "expo-module babel --config-file ./babel.config.build.js --source-maps --out-file build/IconsLazy.js build/Icons.js", "build": "EXPO_NONINTERACTIVE=1 expo-module build && npm run generate-lazy && npm run copy-vendor", "build:dev": "expo-module build", "clean": "expo-module clean", "lint": "eslint tools", "test": "expo-module test", "prepare": "expo-module prepare && npm run generate-lazy && npm run copy-vendor", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "repository": {"type": "git", "url": "https://github.com/expo/vector-icons.git"}, "keywords": ["expo"], "author": "Brent <PERSON>", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/evanbacon)"], "license": "MIT", "bugs": {"url": "https://github.com/expo/vector-icons/issues"}, "jest": {"preset": "expo-module-scripts"}, "homepage": "https://expo.github.io/vector-icons", "devDependencies": {"@types/react-native": "~0.64", "expo-font": "^10.0.5", "expo-module-scripts": "^1.0.0"}}