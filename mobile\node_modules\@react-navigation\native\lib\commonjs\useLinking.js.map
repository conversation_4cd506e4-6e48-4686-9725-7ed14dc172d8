{"version": 3, "names": ["findMatchingState", "a", "b", "undefined", "key", "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "history", "length", "routes", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aRoute", "index", "bRoute", "aChildState", "state", "bChildState", "series", "cb", "queue", "Promise", "resolve", "callback", "then", "linkingHandlers", "useLinking", "ref", "independent", "enabled", "config", "getStateFromPath", "getStateFromPathDefault", "getPathFromState", "getPathFromStateDefault", "getActionFromState", "getActionFromStateDefault", "React", "useEffect", "process", "env", "NODE_ENV", "console", "error", "join", "trim", "handler", "Symbol", "push", "indexOf", "splice", "useState", "createMemoryHistory", "enabledRef", "useRef", "configRef", "getStateFromPathRef", "getPathFromStateRef", "getActionFromStateRef", "current", "server", "useContext", "ServerContext", "getInitialState", "useCallback", "value", "location", "window", "path", "pathname", "search", "thenable", "onfulfilled", "catch", "previousIndexRef", "previousStateRef", "pendingPopStatePathRef", "listen", "navigation", "previousIndex", "record", "get", "resetRoot", "rootState", "getRootState", "some", "r", "routeNames", "includes", "name", "warn", "action", "dispatch", "e", "message", "getPathForRoute", "route", "stateForPath", "focusedRoute", "findFocusedRoute", "isEqual", "params", "replace", "onStateChange", "previousState", "pending<PERSON><PERSON>", "previousFocusedState", "focusedState", "history<PERSON><PERSON><PERSON>", "nextIndex", "backIndex", "currentIndex", "go", "addListener"], "sourceRoot": "../../src", "sources": ["useLinking.tsx"], "mappings": ";;;;;;;AAAA;AASA;AACA;AAEA;AACA;AAA4C;AAAA;AAAA;AAK5C;AACA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAG,CACxBC,CAAgB,EAChBC,CAAgB,KACmB;EACnC,IAAID,CAAC,KAAKE,SAAS,IAAID,CAAC,KAAKC,SAAS,IAAIF,CAAC,CAACG,GAAG,KAAKF,CAAC,CAACE,GAAG,EAAE;IACzD,OAAO,CAACD,SAAS,EAAEA,SAAS,CAAC;EAC/B;;EAEA;EACA,MAAME,cAAc,GAAGJ,CAAC,CAACK,OAAO,GAAGL,CAAC,CAACK,OAAO,CAACC,MAAM,GAAGN,CAAC,CAACO,MAAM,CAACD,MAAM;EACrE,MAAME,cAAc,GAAGP,CAAC,CAACI,OAAO,GAAGJ,CAAC,CAACI,OAAO,CAACC,MAAM,GAAGL,CAAC,CAACM,MAAM,CAACD,MAAM;EAErE,MAAMG,MAAM,GAAGT,CAAC,CAACO,MAAM,CAACP,CAAC,CAACU,KAAK,CAAC;EAChC,MAAMC,MAAM,GAAGV,CAAC,CAACM,MAAM,CAACN,CAAC,CAACS,KAAK,CAAC;EAEhC,MAAME,WAAW,GAAGH,MAAM,CAACI,KAAsB;EACjD,MAAMC,WAAW,GAAGH,MAAM,CAACE,KAAsB;;EAEjD;EACA;EACA;EACA;EACA;EACA,IACET,cAAc,KAAKI,cAAc,IACjCC,MAAM,CAACN,GAAG,KAAKQ,MAAM,CAACR,GAAG,IACzBS,WAAW,KAAKV,SAAS,IACzBY,WAAW,KAAKZ,SAAS,IACzBU,WAAW,CAACT,GAAG,KAAKW,WAAW,CAACX,GAAG,EACnC;IACA,OAAO,CAACH,CAAC,EAAEC,CAAC,CAAC;EACf;EAEA,OAAOF,iBAAiB,CAACa,WAAW,EAAEE,WAAW,CAAC;AACpD,CAAC;;AAED;AACA;AACA;AACO,MAAMC,MAAM,GAAIC,EAAuB,IAAK;EACjD,IAAIC,KAAK,GAAGC,OAAO,CAACC,OAAO,EAAE;EAC7B,MAAMC,QAAQ,GAAG,MAAM;IACrBH,KAAK,GAAGA,KAAK,CAACI,IAAI,CAACL,EAAE,CAAC;EACxB,CAAC;EACD,OAAOI,QAAQ;AACjB,CAAC;AAAC;AAEF,IAAIE,eAAyB,GAAG,EAAE;AAMnB,SAASC,UAAU,CAChCC,GAA2D,QAS3D;EAAA,IARA;IACEC,WAAW;IACXC,OAAO,GAAG,IAAI;IACdC,MAAM;IACNC,gBAAgB,GAAGC,sBAAuB;IAC1CC,gBAAgB,GAAGC,sBAAuB;IAC1CC,kBAAkB,GAAGC;EACd,CAAC;EAEVC,KAAK,CAACC,SAAS,CAAC,MAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,OAAOpC,SAAS;IAClB;IAEA,IAAIuB,WAAW,EAAE;MACf,OAAOvB,SAAS;IAClB;IAEA,IAAIwB,OAAO,KAAK,KAAK,IAAIJ,eAAe,CAAChB,MAAM,EAAE;MAC/CiC,OAAO,CAACC,KAAK,CACX,CACE,6KAA6K,EAC7K,uFAAuF,EACvF,4DAA4D,CAC7D,CACEC,IAAI,CAAC,IAAI,CAAC,CACVC,IAAI,EAAE,CACV;IACH;IAEA,MAAMC,OAAO,GAAGC,MAAM,EAAE;IAExB,IAAIlB,OAAO,KAAK,KAAK,EAAE;MACrBJ,eAAe,CAACuB,IAAI,CAACF,OAAO,CAAC;IAC/B;IAEA,OAAO,MAAM;MACX,MAAMjC,KAAK,GAAGY,eAAe,CAACwB,OAAO,CAACH,OAAO,CAAC;MAE9C,IAAIjC,KAAK,GAAG,CAAC,CAAC,EAAE;QACdY,eAAe,CAACyB,MAAM,CAACrC,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,CAACgB,OAAO,EAAED,WAAW,CAAC,CAAC;EAE1B,MAAM,CAACpB,OAAO,CAAC,GAAG6B,KAAK,CAACc,QAAQ,CAACC,4BAAmB,CAAC;;EAErD;EACA;EACA;EACA,MAAMC,UAAU,GAAGhB,KAAK,CAACiB,MAAM,CAACzB,OAAO,CAAC;EACxC,MAAM0B,SAAS,GAAGlB,KAAK,CAACiB,MAAM,CAACxB,MAAM,CAAC;EACtC,MAAM0B,mBAAmB,GAAGnB,KAAK,CAACiB,MAAM,CAACvB,gBAAgB,CAAC;EAC1D,MAAM0B,mBAAmB,GAAGpB,KAAK,CAACiB,MAAM,CAACrB,gBAAgB,CAAC;EAC1D,MAAMyB,qBAAqB,GAAGrB,KAAK,CAACiB,MAAM,CAACnB,kBAAkB,CAAC;EAE9DE,KAAK,CAACC,SAAS,CAAC,MAAM;IACpBe,UAAU,CAACM,OAAO,GAAG9B,OAAO;IAC5B0B,SAAS,CAACI,OAAO,GAAG7B,MAAM;IAC1B0B,mBAAmB,CAACG,OAAO,GAAG5B,gBAAgB;IAC9C0B,mBAAmB,CAACE,OAAO,GAAG1B,gBAAgB;IAC9CyB,qBAAqB,CAACC,OAAO,GAAGxB,kBAAkB;EACpD,CAAC,CAAC;EAEF,MAAMyB,MAAM,GAAGvB,KAAK,CAACwB,UAAU,CAACC,sBAAa,CAAC;EAE9C,MAAMC,eAAe,GAAG1B,KAAK,CAAC2B,WAAW,CAAC,MAAM;IAC9C,IAAIC,KAA8B;IAElC,IAAIZ,UAAU,CAACM,OAAO,EAAE;MACtB,MAAMO,QAAQ,GACZ,CAAAN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM,QAAQ,MACf,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACD,QAAQ,GAAG7D,SAAS,CAAC;MAE/D,MAAM+D,IAAI,GAAGF,QAAQ,GAAGA,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,MAAM,GAAGjE,SAAS;MAEvE,IAAI+D,IAAI,EAAE;QACRH,KAAK,GAAGT,mBAAmB,CAACG,OAAO,CAACS,IAAI,EAAEb,SAAS,CAACI,OAAO,CAAC;MAC9D;IACF;IAEA,MAAMY,QAAQ,GAAG;MACf/C,IAAI,CAACgD,WAAsD,EAAE;QAC3D,OAAOnD,OAAO,CAACC,OAAO,CAACkD,WAAW,GAAGA,WAAW,CAACP,KAAK,CAAC,GAAGA,KAAK,CAAC;MAClE,CAAC;MACDQ,KAAK,GAAG;QACN,OAAOF,QAAQ;MACjB;IACF,CAAC;IAED,OAAOA,QAAQ;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,gBAAgB,GAAGrC,KAAK,CAACiB,MAAM,CAAqBjD,SAAS,CAAC;EACpE,MAAMsE,gBAAgB,GAAGtC,KAAK,CAACiB,MAAM,CAA8BjD,SAAS,CAAC;EAC7E,MAAMuE,sBAAsB,GAAGvC,KAAK,CAACiB,MAAM,CAAqBjD,SAAS,CAAC;EAE1EgC,KAAK,CAACC,SAAS,CAAC,MAAM;IACpBoC,gBAAgB,CAACf,OAAO,GAAGnD,OAAO,CAACK,KAAK;IAExC,OAAOL,OAAO,CAACqE,MAAM,CAAC,MAAM;MAC1B,MAAMC,UAAU,GAAGnD,GAAG,CAACgC,OAAO;MAE9B,IAAI,CAACmB,UAAU,IAAI,CAACjD,OAAO,EAAE;QAC3B;MACF;MAEA,MAAM;QAAEqC;MAAS,CAAC,GAAGC,MAAM;MAE3B,MAAMC,IAAI,GAAGF,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,MAAM;MAChD,MAAMzD,KAAK,GAAGL,OAAO,CAACK,KAAK;MAE3B,MAAMkE,aAAa,GAAGL,gBAAgB,CAACf,OAAO,IAAI,CAAC;MAEnDe,gBAAgB,CAACf,OAAO,GAAG9C,KAAK;MAChC+D,sBAAsB,CAACjB,OAAO,GAAGS,IAAI;;MAErC;MACA;MACA;MACA,MAAMY,MAAM,GAAGxE,OAAO,CAACyE,GAAG,CAACpE,KAAK,CAAC;MAEjC,IAAI,CAAAmE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEZ,IAAI,MAAKA,IAAI,IAAIY,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEhE,KAAK,EAAE;QAC1C8D,UAAU,CAACI,SAAS,CAACF,MAAM,CAAChE,KAAK,CAAC;QAClC;MACF;MAEA,MAAMA,KAAK,GAAGwC,mBAAmB,CAACG,OAAO,CAACS,IAAI,EAAEb,SAAS,CAACI,OAAO,CAAC;;MAElE;MACA;MACA,IAAI3C,KAAK,EAAE;QACT;QACA;QACA,MAAMmE,SAAS,GAAGL,UAAU,CAACM,YAAY,EAAE;QAE3C,IAAIpE,KAAK,CAACN,MAAM,CAAC2E,IAAI,CAAEC,CAAC,IAAK,EAACH,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEI,UAAU,CAACC,QAAQ,CAACF,CAAC,CAACG,IAAI,CAAC,EAAC,EAAE;UACrE/C,OAAO,CAACgD,IAAI,CACV,0SAA0S,CAC3S;UACD;QACF;QAEA,IAAI7E,KAAK,GAAGkE,aAAa,EAAE;UACzB,MAAMY,MAAM,GAAGjC,qBAAqB,CAACC,OAAO,CAC1C3C,KAAK,EACLuC,SAAS,CAACI,OAAO,CAClB;UAED,IAAIgC,MAAM,KAAKtF,SAAS,EAAE;YACxB,IAAI;cACFyE,UAAU,CAACc,QAAQ,CAACD,MAAM,CAAC;YAC7B,CAAC,CAAC,OAAOE,CAAC,EAAE;cACV;cACA;cACAnD,OAAO,CAACgD,IAAI,CACT,qDAAoDtB,IAAK,MACxD,OAAOyB,CAAC,KAAK,QAAQ,IAAIA,CAAC,IAAI,IAAI,IAAI,SAAS,IAAIA,CAAC,GAChDA,CAAC,CAACC,OAAO,GACTD,CACL,EAAC,CACH;YACH;UACF,CAAC,MAAM;YACLf,UAAU,CAACI,SAAS,CAAClE,KAAK,CAAC;UAC7B;QACF,CAAC,MAAM;UACL8D,UAAU,CAACI,SAAS,CAAClE,KAAK,CAAC;QAC7B;MACF,CAAC,MAAM;QACL;QACA8D,UAAU,CAACI,SAAS,CAAClE,KAAK,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACa,OAAO,EAAErB,OAAO,EAAEmB,GAAG,CAAC,CAAC;EAE3BU,KAAK,CAACC,SAAS,CAAC,MAAM;IAAA;IACpB,IAAI,CAACT,OAAO,EAAE;MACZ;IACF;IAEA,MAAMkE,eAAe,GAAG,CACtBC,KAA0C,EAC1ChF,KAAsB,KACX;MACX;MACA;MACA,IAAIgF,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE5B,IAAI,EAAE;QACf,MAAM6B,YAAY,GAAGzC,mBAAmB,CAACG,OAAO,CAC9CqC,KAAK,CAAC5B,IAAI,EACVb,SAAS,CAACI,OAAO,CAClB;QAED,IAAIsC,YAAY,EAAE;UAChB,MAAMC,YAAY,GAAG,IAAAC,sBAAgB,EAACF,YAAY,CAAC;UAEnD,IACEC,YAAY,IACZA,YAAY,CAACT,IAAI,KAAKO,KAAK,CAACP,IAAI,IAChC,IAAAW,sBAAO,EAACF,YAAY,CAACG,MAAM,EAAEL,KAAK,CAACK,MAAM,CAAC,EAC1C;YACA,OAAOL,KAAK,CAAC5B,IAAI;UACnB;QACF;MACF;MAEA,OAAOX,mBAAmB,CAACE,OAAO,CAAC3C,KAAK,EAAEuC,SAAS,CAACI,OAAO,CAAC;IAC9D,CAAC;IAED,IAAIhC,GAAG,CAACgC,OAAO,EAAE;MACf;MACA;MACA,MAAM3C,KAAK,GAAGW,GAAG,CAACgC,OAAO,CAACyB,YAAY,EAAE;MAExC,IAAIpE,KAAK,EAAE;QACT,MAAMgF,KAAK,GAAG,IAAAG,sBAAgB,EAACnF,KAAK,CAAC;QACrC,MAAMoD,IAAI,GAAG2B,eAAe,CAACC,KAAK,EAAEhF,KAAK,CAAC;QAE1C,IAAI2D,gBAAgB,CAAChB,OAAO,KAAKtD,SAAS,EAAE;UAC1CsE,gBAAgB,CAAChB,OAAO,GAAG3C,KAAK;QAClC;QAEAR,OAAO,CAAC8F,OAAO,CAAC;UAAElC,IAAI;UAAEpD;QAAM,CAAC,CAAC;MAClC;IACF;IAEA,MAAMuF,aAAa,GAAG,YAAY;MAChC,MAAMzB,UAAU,GAAGnD,GAAG,CAACgC,OAAO;MAE9B,IAAI,CAACmB,UAAU,IAAI,CAACjD,OAAO,EAAE;QAC3B;MACF;MAEA,MAAM2E,aAAa,GAAG7B,gBAAgB,CAAChB,OAAO;MAC9C,MAAM3C,KAAK,GAAG8D,UAAU,CAACM,YAAY,EAAE;;MAEvC;MACA,IAAI,CAACpE,KAAK,EAAE;QACV;MACF;MAEA,MAAMyF,WAAW,GAAG7B,sBAAsB,CAACjB,OAAO;MAClD,MAAMqC,KAAK,GAAG,IAAAG,sBAAgB,EAACnF,KAAK,CAAC;MACrC,MAAMoD,IAAI,GAAG2B,eAAe,CAACC,KAAK,EAAEhF,KAAK,CAAC;MAE1C2D,gBAAgB,CAAChB,OAAO,GAAG3C,KAAK;MAChC4D,sBAAsB,CAACjB,OAAO,GAAGtD,SAAS;;MAE1C;MACA;MACA;MACA;MACA,MAAM,CAACqG,oBAAoB,EAAEC,YAAY,CAAC,GAAGzG,iBAAiB,CAC5DsG,aAAa,EACbxF,KAAK,CACN;MAED,IACE0F,oBAAoB,IACpBC,YAAY;MACZ;MACA;MACAvC,IAAI,KAAKqC,WAAW,EACpB;QACA,MAAMG,YAAY,GAChB,CAACD,YAAY,CAACnG,OAAO,GACjBmG,YAAY,CAACnG,OAAO,CAACC,MAAM,GAC3BkG,YAAY,CAACjG,MAAM,CAACD,MAAM,KAC7BiG,oBAAoB,CAAClG,OAAO,GACzBkG,oBAAoB,CAAClG,OAAO,CAACC,MAAM,GACnCiG,oBAAoB,CAAChG,MAAM,CAACD,MAAM,CAAC;QAEzC,IAAImG,YAAY,GAAG,CAAC,EAAE;UACpB;UACA;UACApG,OAAO,CAACwC,IAAI,CAAC;YAAEoB,IAAI;YAAEpD;UAAM,CAAC,CAAC;QAC/B,CAAC,MAAM,IAAI4F,YAAY,GAAG,CAAC,EAAE;UAC3B;;UAEA,MAAMC,SAAS,GAAGrG,OAAO,CAACsG,SAAS,CAAC;YAAE1C;UAAK,CAAC,CAAC;UAC7C,MAAM2C,YAAY,GAAGvG,OAAO,CAACK,KAAK;UAElC,IAAI;YACF,IACEgG,SAAS,KAAK,CAAC,CAAC,IAChBA,SAAS,GAAGE,YAAY;YACxB;YACAvG,OAAO,CAACyE,GAAG,CAAC4B,SAAS,GAAGE,YAAY,CAAC,EACrC;cACA;cACA,MAAMvG,OAAO,CAACwG,EAAE,CAACH,SAAS,GAAGE,YAAY,CAAC;YAC5C,CAAC,MAAM;cACL;cACA;cACA;cACA,MAAMvG,OAAO,CAACwG,EAAE,CAACJ,YAAY,CAAC;YAChC;;YAEA;YACApG,OAAO,CAAC8F,OAAO,CAAC;cAAElC,IAAI;cAAEpD;YAAM,CAAC,CAAC;UAClC,CAAC,CAAC,OAAO6E,CAAC,EAAE;YACV;UAAA;QAEJ,CAAC,MAAM;UACL;UACArF,OAAO,CAAC8F,OAAO,CAAC;YAAElC,IAAI;YAAEpD;UAAM,CAAC,CAAC;QAClC;MACF,CAAC,MAAM;QACL;QACA;QACAR,OAAO,CAAC8F,OAAO,CAAC;UAAElC,IAAI;UAAEpD;QAAM,CAAC,CAAC;MAClC;IACF,CAAC;;IAED;IACA;IACA;IACA,uBAAOW,GAAG,CAACgC,OAAO,iDAAX,aAAasD,WAAW,CAAC,OAAO,EAAE/F,MAAM,CAACqF,aAAa,CAAC,CAAC;EACjE,CAAC,EAAE,CAAC1E,OAAO,EAAErB,OAAO,EAAEmB,GAAG,CAAC,CAAC;EAE3B,OAAO;IACLoC;EACF,CAAC;AACH"}