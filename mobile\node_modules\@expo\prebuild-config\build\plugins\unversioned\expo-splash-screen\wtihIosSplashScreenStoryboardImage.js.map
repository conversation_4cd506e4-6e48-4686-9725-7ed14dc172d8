{"version": 3, "file": "wtihIosSplashScreenStoryboardImage.js", "names": ["_InterfaceBuilder", "data", "require", "_withIosSplashScreenStoryboard", "withIosSplashScreenImage", "config", "splash", "withIosSplashScreenStoryboard", "modResults", "applySplashScreenStoryboard", "exports", "obj", "resizeMode", "splashScreenImagePresent", "Boolean", "image", "imageName", "contentMode", "getImageContentMode", "applyImageToSplashScreenXML", "removeImageFromSplashScreen", "Error"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/wtihIosSplashScreenStoryboardImage.ts"], "sourcesContent": ["import { ConfigPlugin } from '@expo/config-plugins';\n\nimport {\n  applyImageToSplashScreenXML,\n  IBSplashScreenDocument,\n  ImageContentMode,\n  removeImageFromSplashScreen,\n} from './InterfaceBuilder';\nimport { IOSSplashConfig } from './getIosSplashConfig';\nimport { withIosSplashScreenStoryboard } from './withIosSplashScreenStoryboard';\n\nexport const withIosSplashScreenImage: ConfigPlugin<IOSSplashConfig> = (config, splash) => {\n  return withIosSplashScreenStoryboard(config, (config) => {\n    config.modResults = applySplashScreenStoryboard(config.modResults, splash);\n    return config;\n  });\n};\n\nexport function applySplashScreenStoryboard(obj: IBSplashScreenDocument, splash: IOSSplashConfig) {\n  const resizeMode = splash?.resizeMode;\n  const splashScreenImagePresent = Boolean(splash?.image);\n  const imageName = 'SplashScreen';\n  // Only get the resize mode when the image is present.\n  if (splashScreenImagePresent) {\n    const contentMode = getImageContentMode(resizeMode || 'contain');\n    return applyImageToSplashScreenXML(obj, {\n      contentMode,\n      imageName,\n    });\n  }\n\n  return removeImageFromSplashScreen(obj, { imageName });\n}\n\nfunction getImageContentMode(resizeMode: string): ImageContentMode {\n  switch (resizeMode) {\n    case 'contain':\n      return 'scaleAspectFit';\n    case 'cover':\n      return 'scaleAspectFill';\n    default:\n      throw new Error(`{ resizeMode: \"${resizeMode}\" } is not supported for iOS platform.`);\n  }\n}\n"], "mappings": ";;;;;;;AAEA,SAAAA,kBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,iBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAOA,SAAAE,+BAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,8BAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,wBAAuD,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACzF,OAAO,IAAAC,8DAA6B,EAACF,MAAM,EAAGA,MAAM,IAAK;IACvDA,MAAM,CAACG,UAAU,GAAGC,2BAA2B,CAACJ,MAAM,CAACG,UAAU,EAAEF,MAAM,CAAC;IAC1E,OAAOD,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAN,wBAAA,GAAAA,wBAAA;AAEK,SAASK,2BAA2BA,CAACE,GAA2B,EAAEL,MAAuB,EAAE;EAChG,MAAMM,UAAU,GAAGN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM,UAAU;EACrC,MAAMC,wBAAwB,GAAGC,OAAO,CAACR,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES,KAAK,CAAC;EACvD,MAAMC,SAAS,GAAG,cAAc;EAChC;EACA,IAAIH,wBAAwB,EAAE;IAC5B,MAAMI,WAAW,GAAGC,mBAAmB,CAACN,UAAU,IAAI,SAAS,CAAC;IAChE,OAAO,IAAAO,+CAA2B,EAACR,GAAG,EAAE;MACtCM,WAAW;MACXD;IACF,CAAC,CAAC;EACJ;EAEA,OAAO,IAAAI,+CAA2B,EAACT,GAAG,EAAE;IAAEK;EAAU,CAAC,CAAC;AACxD;AAEA,SAASE,mBAAmBA,CAACN,UAAkB,EAAoB;EACjE,QAAQA,UAAU;IAChB,KAAK,SAAS;MACZ,OAAO,gBAAgB;IACzB,KAAK,OAAO;MACV,OAAO,iBAAiB;IAC1B;MACE,MAAM,IAAIS,KAAK,CAAE,kBAAiBT,UAAW,wCAAuC,CAAC;EAAC;AAE5F"}