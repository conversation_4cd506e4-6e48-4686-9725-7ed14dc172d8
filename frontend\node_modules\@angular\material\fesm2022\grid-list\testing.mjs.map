{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/grid-list/testing/grid-tile-harness.ts", "../../../../../../../src/material/grid-list/testing/grid-list-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ContentContainerComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {GridTileHarnessFilters} from './grid-list-harness-filters';\n\n/** Selectors for the various `mat-grid-tile` sections that may contain user content. */\nexport enum MatGridTileSection {\n  HEADER = '.mat-grid-tile-header',\n  FOOTER = '.mat-grid-tile-footer',\n}\n\n/** Harness for interacting with a standard `MatGridTitle` in tests. */\nexport class MatGridTileHarness extends ContentContainerComponentHarness<MatGridTileSection> {\n  /** The selector for the host element of a `MatGridTile` instance. */\n  static hostSelector = '.mat-grid-tile';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatGridTileHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which dialog instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: GridTileHarnessFilters = {}): HarnessPredicate<MatGridTileHarness> {\n    return new HarnessPredicate(MatGridTileHarness, options)\n      .addOption('headerText', options.headerText, (harness, pattern) =>\n        HarnessPredicate.stringMatches(harness.getHeaderText(), pattern),\n      )\n      .addOption('footerText', options.footerText, (harness, pattern) =>\n        HarnessPredicate.stringMatches(harness.getFooterText(), pattern),\n      );\n  }\n\n  private _header = this.locatorForOptional(MatGridTileSection.HEADER);\n  private _footer = this.locatorForOptional(MatGridTileSection.FOOTER);\n  private _avatar = this.locatorForOptional('.mat-grid-avatar');\n\n  /** Gets the amount of rows that the grid-tile takes up. */\n  async getRowspan(): Promise<number> {\n    return Number(await (await this.host()).getAttribute('rowspan'));\n  }\n\n  /** Gets the amount of columns that the grid-tile takes up. */\n  async getColspan(): Promise<number> {\n    return Number(await (await this.host()).getAttribute('colspan'));\n  }\n\n  /** Whether the grid-tile has a header. */\n  async hasHeader(): Promise<boolean> {\n    return (await this._header()) !== null;\n  }\n\n  /** Whether the grid-tile has a footer. */\n  async hasFooter(): Promise<boolean> {\n    return (await this._footer()) !== null;\n  }\n\n  /** Whether the grid-tile has an avatar. */\n  async hasAvatar(): Promise<boolean> {\n    return (await this._avatar()) !== null;\n  }\n\n  /** Gets the text of the header if present. */\n  async getHeaderText(): Promise<string | null> {\n    // For performance reasons, we do not use \"hasHeader\" as\n    // we would then need to query twice for the header.\n    const headerEl = await this._header();\n    return headerEl ? headerEl.text() : null;\n  }\n\n  /** Gets the text of the footer if present. */\n  async getFooterText(): Promise<string | null> {\n    // For performance reasons, we do not use \"hasFooter\" as\n    // we would then need to query twice for the footer.\n    const headerEl = await this._footer();\n    return headerEl ? headerEl.text() : null;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentHarness, HarnessPredicate, parallel} from '@angular/cdk/testing';\nimport {ɵTileCoordinator as TileCoordinator} from '@angular/material/grid-list';\nimport {GridListHarnessFilters, GridTileHarnessFilters} from './grid-list-harness-filters';\nimport {MatGridTileHarness} from './grid-tile-harness';\n\n/** <PERSON><PERSON>ss for interacting with a standard `MatGridList` in tests. */\nexport class MatGridListHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatGridList` instance. */\n  static hostSelector = '.mat-grid-list';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatGridListHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which dialog instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: GridListHarnessFilters = {}): HarnessPredicate<MatGridListHarness> {\n    return new HarnessPredicate(MatGridListHarness, options);\n  }\n\n  /**\n   * Tile coordinator that is used by the \"MatGridList\" for computing\n   * positions of tiles. We leverage the coordinator to provide an API\n   * for retrieving tiles based on visual tile positions.\n   */\n  private _tileCoordinator = new TileCoordinator();\n\n  /** Gets all tiles of the grid-list. */\n  async getTiles(filters: GridTileHarnessFilters = {}): Promise<MatGridTileHarness[]> {\n    return await this.locatorForAll(MatGridTileHarness.with(filters))();\n  }\n\n  /** Gets the amount of columns of the grid-list. */\n  async getColumns(): Promise<number> {\n    return Number(await (await this.host()).getAttribute('cols'));\n  }\n\n  /**\n   * Gets a tile of the grid-list that is located at the given location.\n   * @param row Zero-based row index.\n   * @param column Zero-based column index.\n   */\n  async getTileAtPosition({\n    row,\n    column,\n  }: {\n    row: number;\n    column: number;\n  }): Promise<MatGridTileHarness> {\n    const [tileHarnesses, columns] = await parallel(() => [this.getTiles(), this.getColumns()]);\n    const tileSpans = tileHarnesses.map(t => parallel(() => [t.getColspan(), t.getRowspan()]));\n    const tiles = (await parallel(() => tileSpans)).map(([colspan, rowspan]) => ({\n      colspan,\n      rowspan,\n    }));\n    // Update the tile coordinator to reflect the current column amount and\n    // rendered tiles. We update upon every call of this method since we do not\n    // know if tiles have been added, removed or updated (in terms of rowspan/colspan).\n    this._tileCoordinator.update(columns, tiles);\n    // The tile coordinator respects the colspan and rowspan for calculating the positions\n    // of tiles, but it does not create multiple position entries if a tile spans over multiple\n    // columns or rows. We want to provide an API where developers can retrieve a tile based on\n    // any position that lies within the visual tile boundaries. For example: If a tile spans\n    // over two columns, then the same tile should be returned for either column indices.\n    for (let i = 0; i < this._tileCoordinator.positions.length; i++) {\n      const position = this._tileCoordinator.positions[i];\n      const {rowspan, colspan} = tiles[i];\n      // Return the tile harness if the given position visually resolves to the tile.\n      if (\n        column >= position.col &&\n        column <= position.col + colspan - 1 &&\n        row >= position.row &&\n        row <= position.row + rowspan - 1\n      ) {\n        return tileHarnesses[i];\n      }\n    }\n    throw Error('Could not find tile at given position.');\n  }\n}\n"], "names": ["TileCoordinator"], "mappings": ";;;AAWA;IACY,mBAGX;AAHD,CAAA,UAAY,kBAAkB,EAAA;AAC5B,IAAA,kBAAA,CAAA,QAAA,CAAA,GAAA,uBAAgC,CAAA;AAChC,IAAA,kBAAA,CAAA,QAAA,CAAA,GAAA,uBAAgC,CAAA;AAClC,CAAC,EAHW,kBAAkB,KAAlB,kBAAkB,GAG7B,EAAA,CAAA,CAAA,CAAA;AAED;AACM,MAAO,kBAAmB,SAAQ,gCAAoD,CAAA;AAA5F,IAAA,WAAA,GAAA;;QAoBU,IAAO,CAAA,OAAA,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC7D,IAAO,CAAA,OAAA,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAC7D,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;KA0C/D;;aA9DQ,IAAY,CAAA,YAAA,GAAG,gBAAH,CAAoB,EAAA;AAEvC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAkC,EAAE,EAAA;AAC9C,QAAA,OAAO,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC;aACrD,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,OAAO,KAC5D,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,OAAO,CAAC,CACjE;aACA,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,OAAO,KAC5D,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,OAAO,CAAC,CACjE,CAAC;KACL;;AAOD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;KAClE;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;KAClE;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC;KACxC;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC;KACxC;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC;KACxC;;AAGD,IAAA,MAAM,aAAa,GAAA;;;AAGjB,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AACtC,QAAA,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC;KAC1C;;AAGD,IAAA,MAAM,aAAa,GAAA;;;AAGjB,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AACtC,QAAA,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC;KAC1C;;;ACpEH;AACM,MAAO,kBAAmB,SAAQ,gBAAgB,CAAA;AAAxD,IAAA,WAAA,GAAA;;AAcE;;;;AAIG;AACK,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAIA,gBAAe,EAAE,CAAC;KAsDlD;;aAvEQ,IAAY,CAAA,YAAA,GAAG,gBAAH,CAAoB,EAAA;AAEvC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAkC,EAAE,EAAA;AAC9C,QAAA,OAAO,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;KAC1D;;AAUD,IAAA,MAAM,QAAQ,CAAC,OAAA,GAAkC,EAAE,EAAA;AACjD,QAAA,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;KACrE;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;KAC/D;AAED;;;;AAIG;AACH,IAAA,MAAM,iBAAiB,CAAC,EACtB,GAAG,EACH,MAAM,GAIP,EAAA;QACC,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AAC5F,QAAA,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3F,MAAM,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC,MAAM,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM;YAC3E,OAAO;YACP,OAAO;AACR,SAAA,CAAC,CAAC,CAAC;;;;QAIJ,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;;;;;;AAM7C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,EAAC,OAAO,EAAE,OAAO,EAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;;AAEpC,YAAA,IACE,MAAM,IAAI,QAAQ,CAAC,GAAG;AACtB,gBAAA,MAAM,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,GAAG,CAAC;gBACpC,GAAG,IAAI,QAAQ,CAAC,GAAG;gBACnB,GAAG,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,GAAG,CAAC,EACjC;AACA,gBAAA,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC;aACzB;SACF;AACD,QAAA,MAAM,KAAK,CAAC,wCAAwC,CAAC,CAAC;KACvD;;;;;"}