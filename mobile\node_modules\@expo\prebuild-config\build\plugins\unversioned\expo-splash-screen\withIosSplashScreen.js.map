{"version": 3, "file": "withIosSplashScreen.js", "names": ["_configPlugins", "data", "require", "_debug", "_interopRequireDefault", "_getIosSplashConfig", "_withIosSplashAssets", "_withIosSplashInfoPlist", "_withIosSplashScreenStoryboard", "_withIosSplashXcodeProject", "_wtihIosSplashScreenStoryboardImage", "obj", "__esModule", "default", "debug", "Debug", "withIosSplashScreen", "config", "splash", "getIosSplashConfig", "with<PERSON><PERSON><PERSON>", "withIosSplashInfoPlist", "withIosSplashAssets", "withIosSplashScreenImage", "withIosSplashXcodeProject", "withIosSplashScreenStoryboardBaseMod", "exports"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashScreen.ts"], "sourcesContent": ["import { ConfigPlugin, withPlugins } from '@expo/config-plugins';\nimport Debug from 'debug';\n\nimport { getIosSplashConfig, IOSSplashConfig } from './getIosSplashConfig';\nimport { withIosSplashAssets } from './withIosSplashAssets';\nimport { withIosSplashInfoPlist } from './withIosSplashInfoPlist';\nimport { withIosSplashScreenStoryboardBaseMod } from './withIosSplashScreenStoryboard';\nimport { withIosSplashXcodeProject } from './withIosSplashXcodeProject';\nimport { withIosSplashScreenImage } from './wtihIosSplashScreenStoryboardImage';\n\nconst debug = Debug('expo:prebuild-config:expo-splash-screen:ios');\n\nexport const withIosSplashScreen: ConfigPlugin<IOSSplashConfig | undefined | null | void> = (\n  config,\n  splash\n) => {\n  // If the user didn't specify a splash object, infer the splash object from the Expo config.\n  if (!splash) {\n    splash = getIosSplashConfig(config);\n  } else {\n    debug(`custom splash config provided`);\n  }\n\n  debug(`config:`, splash);\n\n  return withPlugins(config, [\n    [withIosSplashInfoPlist, splash],\n    [withIosSplashAssets, splash],\n    // Add the image settings to the storyboard.\n    [withIosSplashScreenImage, splash],\n    // Link storyboard to xcode project.\n    // TODO: Maybe fold this into the base mod.\n    withIosSplashXcodeProject,\n    // Insert the base mod last, no other ios.splashScreenStoryboard mods can be added after this.\n    withIosSplashScreenStoryboardBaseMod,\n  ]);\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,oBAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,mBAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,qBAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,oBAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,wBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,uBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,+BAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,8BAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,2BAAA;EAAA,MAAAR,IAAA,GAAAC,OAAA;EAAAO,0BAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,oCAAA;EAAA,MAAAT,IAAA,GAAAC,OAAA;EAAAQ,mCAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAgF,SAAAG,uBAAAO,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEhF,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,6CAA6C,CAAC;AAE3D,MAAMC,mBAA4E,GAAGA,CAC1FC,MAAM,EACNC,MAAM,KACH;EACH;EACA,IAAI,CAACA,MAAM,EAAE;IACXA,MAAM,GAAG,IAAAC,wCAAkB,EAACF,MAAM,CAAC;EACrC,CAAC,MAAM;IACLH,KAAK,CAAE,+BAA8B,CAAC;EACxC;EAEAA,KAAK,CAAE,SAAQ,EAAEI,MAAM,CAAC;EAExB,OAAO,IAAAE,4BAAW,EAACH,MAAM,EAAE,CACzB,CAACI,gDAAsB,EAAEH,MAAM,CAAC,EAChC,CAACI,0CAAmB,EAAEJ,MAAM,CAAC;EAC7B;EACA,CAACK,8DAAwB,EAAEL,MAAM,CAAC;EAClC;EACA;EACAM,sDAAyB;EACzB;EACAC,qEAAoC,CACrC,CAAC;AACJ,CAAC;AAACC,OAAA,CAAAV,mBAAA,GAAAA,mBAAA"}