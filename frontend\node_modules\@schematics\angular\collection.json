{"schematics": {"ng-new": {"factory": "./ng-new", "schema": "./ng-new/schema.json", "description": "Create an Angular workspace.", "hidden": true}, "workspace": {"factory": "./workspace", "schema": "./workspace/schema.json", "description": "Create an Angular workspace.", "hidden": true}, "service-worker": {"factory": "./service-worker", "description": "Initializes a service worker setup.", "schema": "./service-worker/schema.json"}, "application": {"aliases": ["app"], "factory": "./application", "schema": "./application/schema.json", "description": "Create an Angular application."}, "e2e": {"factory": "./e2e", "schema": "./e2e/schema.json", "description": "Create an Angular e2e application.", "hidden": true}, "class": {"aliases": ["cl"], "factory": "./class", "description": "Create a class.", "schema": "./class/schema.json"}, "component": {"aliases": ["c"], "factory": "./component", "description": "Create an Angular component.", "schema": "./component/schema.json"}, "directive": {"aliases": ["d"], "factory": "./directive", "description": "Create an Angular directive.", "schema": "./directive/schema.json"}, "enum": {"aliases": ["e"], "factory": "./enum", "description": "Create an enumeration.", "schema": "./enum/schema.json"}, "guard": {"aliases": ["g"], "factory": "./guard", "description": "Create a guard.", "schema": "./guard/schema.json"}, "resolver": {"aliases": ["r"], "factory": "./resolver", "description": "Create a resolver.", "schema": "./resolver/schema.json"}, "interceptor": {"factory": "./interceptor", "description": "Create an interceptor.", "schema": "./interceptor/schema.json"}, "interface": {"aliases": ["i"], "factory": "./interface", "description": "Create an interface.", "schema": "./interface/schema.json"}, "module": {"aliases": ["m"], "factory": "./module", "description": "Create an Angular module.", "schema": "./module/schema.json"}, "pipe": {"aliases": ["p"], "factory": "./pipe", "description": "Create an Angular pipe.", "schema": "./pipe/schema.json"}, "service": {"aliases": ["s"], "factory": "./service", "description": "Create an Angular service.", "schema": "./service/schema.json"}, "server": {"factory": "./server", "description": "Create an Angular server app.", "schema": "./server/schema.json", "hidden": true}, "ssr": {"factory": "./ssr", "description": "Adds SSR to an Angular app.", "schema": "./ssr/schema.json", "hidden": true}, "app-shell": {"factory": "./app-shell", "description": "Create an application shell.", "schema": "./app-shell/schema.json"}, "library": {"aliases": ["lib"], "factory": "./library", "schema": "./library/schema.json", "description": "Generate a library project for Angular."}, "web-worker": {"factory": "./web-worker", "schema": "./web-worker/schema.json", "description": "Create a Web Worker."}, "environments": {"factory": "./environments", "schema": "./environments/schema.json", "description": "Generate project environment files."}, "config": {"factory": "./config", "schema": "./config/schema.json", "description": "Generates a configuration file."}}}