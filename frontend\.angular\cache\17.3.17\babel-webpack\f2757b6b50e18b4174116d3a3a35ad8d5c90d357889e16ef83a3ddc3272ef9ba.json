{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/progress-spinner\";\nfunction DashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"h1\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Welcome to your E-Commerce Management Dashboard\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r1 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.getGreeting(), \", \", user_r1.name, \"!\");\n  }\n}\nfunction DashboardComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"mat-spinner\", 10);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"mat-icon\", 12);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_10_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshDashboard());\n    });\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction DashboardComponent_div_11_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.stats.products.low_stock, \" Low Stock \");\n  }\n}\nfunction DashboardComponent_div_11_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"p\", 37);\n    i0.ɵɵtext(2, \"Sales Chart (Chart.js integration needed)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_11_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No sales data available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_11_div_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"p\", 37);\n    i0.ɵɵtext(2, \"Orders Chart (Chart.js integration needed)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_11_div_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"show_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No orders data available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_11_div_127_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"mat-icon\", 43);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 44)(5, \"p\", 45);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 46)(8, \"span\", 47);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 48);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const activity_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", ctx_r1.getActivityColor(activity_r5.subject_type));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActivityIcon(activity_r5.subject_type), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r5.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"by \", activity_r5.causer.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(activity_r5.created_at));\n  }\n}\nfunction DashboardComponent_div_11_div_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, DashboardComponent_div_11_div_127_div_1_Template, 12, 5, \"div\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentActivities);\n  }\n}\nfunction DashboardComponent_div_11_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No recent activities\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"mat-card\", 15)(3, \"mat-card-header\")(4, \"div\", 16)(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"people\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-card-title\");\n    i0.ɵɵtext(8, \"Users\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-card-subtitle\");\n    i0.ɵɵtext(10, \"Total registered users\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"div\", 17);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 18)(16, \"span\", 19)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 19)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"mat-card-actions\")(25, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_11_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToModule(\"users\"));\n    });\n    i0.ɵɵtext(26, \" View All \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"mat-card\", 21)(28, \"mat-card-header\")(29, \"div\", 22)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"inventory\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"mat-card-title\");\n    i0.ɵɵtext(33, \"Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"mat-card-subtitle\");\n    i0.ɵɵtext(35, \"Product catalog\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"mat-card-content\")(37, \"div\", 17);\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 18)(41, \"span\", 19)(42, \"mat-icon\");\n    i0.ɵɵtext(43, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, DashboardComponent_div_11_span_45_Template, 4, 1, \"span\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"mat-card-actions\")(47, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_11_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToModule(\"products\"));\n    });\n    i0.ɵɵtext(48, \" View All \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"mat-card\", 24)(50, \"mat-card-header\")(51, \"div\", 25)(52, \"mat-icon\");\n    i0.ɵɵtext(53, \"shopping_cart\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"mat-card-title\");\n    i0.ɵɵtext(55, \"Orders\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"mat-card-subtitle\");\n    i0.ɵɵtext(57, \"Customer orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"mat-card-content\")(59, \"div\", 17);\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"div\", 18)(63, \"span\", 19)(64, \"mat-icon\");\n    i0.ɵɵtext(65, \"pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"span\", 19)(68, \"mat-icon\");\n    i0.ɵɵtext(69, \"today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"mat-card-actions\")(72, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_11_Template_button_click_72_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToModule(\"orders\"));\n    });\n    i0.ɵɵtext(73, \" View All \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"mat-card\", 26)(75, \"mat-card-header\")(76, \"div\", 27)(77, \"mat-icon\");\n    i0.ɵɵtext(78, \"attach_money\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(79, \"mat-card-title\");\n    i0.ɵɵtext(80, \"Revenue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"mat-card-subtitle\");\n    i0.ɵɵtext(82, \"Total earnings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"mat-card-content\")(84, \"div\", 17);\n    i0.ɵɵtext(85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"div\", 18)(87, \"span\", 19)(88, \"mat-icon\");\n    i0.ɵɵtext(89, \"today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"span\", 19)(92, \"mat-icon\");\n    i0.ɵɵtext(93, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(94);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"mat-card-actions\")(96, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_11_Template_button_click_96_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToModule(\"analytics\"));\n    });\n    i0.ɵɵtext(97, \" View Reports \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(98, \"div\", 28)(99, \"div\", 29)(100, \"mat-card\")(101, \"mat-card-header\")(102, \"mat-card-title\");\n    i0.ɵɵtext(103, \"Sales Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"mat-card-subtitle\");\n    i0.ɵɵtext(105, \"Last 7 days\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(106, \"mat-card-content\");\n    i0.ɵɵtemplate(107, DashboardComponent_div_11_div_107_Template, 3, 0, \"div\", 30)(108, DashboardComponent_div_11_div_108_Template, 5, 0, \"div\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(109, \"div\", 29)(110, \"mat-card\")(111, \"mat-card-header\")(112, \"mat-card-title\");\n    i0.ɵɵtext(113, \"Orders Trend\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"mat-card-subtitle\");\n    i0.ɵɵtext(115, \"Last 7 days\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(116, \"mat-card-content\");\n    i0.ɵɵtemplate(117, DashboardComponent_div_11_div_117_Template, 3, 0, \"div\", 30)(118, DashboardComponent_div_11_div_118_Template, 5, 0, \"div\", 31);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(119, \"div\", 32)(120, \"mat-card\")(121, \"mat-card-header\")(122, \"mat-card-title\");\n    i0.ɵɵtext(123, \"Recent Activities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(124, \"mat-card-subtitle\");\n    i0.ɵɵtext(125, \"Latest system activities\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(126, \"mat-card-content\");\n    i0.ɵɵtemplate(127, DashboardComponent_div_11_div_127_Template, 2, 1, \"div\", 33)(128, DashboardComponent_div_11_div_128_Template, 5, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(129, \"mat-card-actions\")(130, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_11_Template_button_click_130_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToModule(\"activity-log\"));\n    });\n    i0.ɵɵtext(131, \" View All Activities \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 18, ctx_r1.stats.users.total));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.stats.users.active, \" Active \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.stats.users.new_today, \" New Today \");\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(39, 20, ctx_r1.stats.products.total));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.stats.products.published, \" Published \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.stats.products.low_stock > 0);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(61, 22, ctx_r1.stats.orders.total));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.stats.orders.pending, \" Pending \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.stats.orders.orders_today, \" Today \");\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCurrency(ctx_r1.stats.orders.total_revenue));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatCurrency(ctx_r1.stats.orders.revenue_today), \" Today \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.stats.orders.delivered, \" Completed \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.salesChartData.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.salesChartData.length === 0);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ordersChartData.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ordersChartData.length === 0);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.recentActivities.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.recentActivities.length === 0);\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    constructor() {\n      this.stats = null;\n      this.recentActivities = [];\n      this.loading = false;\n      this.error = null;\n      // Mock data for now\n      this.mockStats = {\n        users: {\n          total: 150,\n          active: 120,\n          new_today: 5\n        },\n        products: {\n          total: 500,\n          published: 450,\n          out_of_stock: 10,\n          low_stock: 25\n        },\n        orders: {\n          total: 1200,\n          pending: 15,\n          processing: 8,\n          delivered: 1177,\n          total_revenue: 45000,\n          orders_today: 12,\n          revenue_today: 850\n        },\n        categories: {\n          total: 25,\n          active: 22,\n          featured: 8\n        }\n      };\n    }\n    ngOnInit() {\n      // Load mock data for now\n      this.stats = this.mockStats;\n      this.recentActivities = [{\n        id: 1,\n        description: 'New user registered',\n        subject_type: 'App\\\\Models\\\\User',\n        subject_id: 123,\n        causer: {\n          id: 1,\n          name: 'System'\n        },\n        created_at: new Date().toISOString()\n      }, {\n        id: 2,\n        description: 'Product updated',\n        subject_type: 'App\\\\Models\\\\Product',\n        subject_id: 456,\n        causer: {\n          id: 2,\n          name: 'Admin'\n        },\n        created_at: new Date(Date.now() - 3600000).toISOString()\n      }];\n    }\n    getGreeting() {\n      const hour = new Date().getHours();\n      if (hour < 12) {\n        return 'Good morning';\n      } else if (hour < 18) {\n        return 'Good afternoon';\n      } else {\n        return 'Good evening';\n      }\n    }\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n      }).format(amount);\n    }\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getActivityIcon(subjectType) {\n      const iconMap = {\n        'App\\\\Models\\\\User': 'person',\n        'App\\\\Models\\\\Product': 'inventory',\n        'App\\\\Models\\\\Order': 'shopping_cart',\n        'App\\\\Models\\\\Category': 'category',\n        'App\\\\Models\\\\Coupon': 'local_offer',\n        'App\\\\Models\\\\Media': 'image',\n        'default': 'info'\n      };\n      return iconMap[subjectType] || iconMap['default'];\n    }\n    getActivityColor(subjectType) {\n      const colorMap = {\n        'App\\\\Models\\\\User': 'primary',\n        'App\\\\Models\\\\Product': 'accent',\n        'App\\\\Models\\\\Order': 'warn',\n        'App\\\\Models\\\\Category': 'primary',\n        'App\\\\Models\\\\Coupon': 'accent',\n        'App\\\\Models\\\\Media': 'primary',\n        'default': ''\n      };\n      return colorMap[subjectType] || colorMap['default'];\n    }\n    refreshDashboard() {\n      console.log('Refreshing dashboard...');\n      // Reload mock data\n      this.ngOnInit();\n    }\n    navigateToModule(module) {\n      // Navigation logic will be implemented when routing is set up\n      console.log(`Navigate to ${module}`);\n    }\n    static {\n      this.ɵfac = function DashboardComponent_Factory(t) {\n        return new (t || DashboardComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 12,\n        vars: 6,\n        consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [\"class\", \"welcome-section\", 4, \"ngIf\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"welcome-section\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"error-container\"], [\"color\", \"warn\"], [1, \"dashboard-content\"], [1, \"stats-grid\"], [1, \"stat-card\", \"users-card\"], [\"mat-card-avatar\", \"\", 1, \"stat-avatar\", \"users-avatar\"], [1, \"stat-number\"], [1, \"stat-details\"], [1, \"stat-item\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"stat-card\", \"products-card\"], [\"mat-card-avatar\", \"\", 1, \"stat-avatar\", \"products-avatar\"], [\"class\", \"stat-item warning\", 4, \"ngIf\"], [1, \"stat-card\", \"orders-card\"], [\"mat-card-avatar\", \"\", 1, \"stat-avatar\", \"orders-avatar\"], [1, \"stat-card\", \"revenue-card\"], [\"mat-card-avatar\", \"\", 1, \"stat-avatar\", \"revenue-avatar\"], [1, \"charts-section\"], [1, \"chart-container\"], [\"class\", \"chart-wrapper\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [1, \"activities-section\"], [\"class\", \"activities-list\", 4, \"ngIf\"], [\"class\", \"no-activities\", 4, \"ngIf\"], [1, \"stat-item\", \"warning\"], [1, \"chart-wrapper\"], [1, \"chart-placeholder\"], [1, \"no-data\"], [1, \"activities-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activity-item\"], [1, \"activity-icon\"], [3, \"color\"], [1, \"activity-content\"], [1, \"activity-description\"], [1, \"activity-meta\"], [1, \"activity-user\"], [1, \"activity-time\"], [1, \"no-activities\"]],\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵtemplate(2, DashboardComponent_div_2_Template, 5, 2, \"div\", 2);\n            i0.ɵɵpipe(3, \"async\");\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_5_listener() {\n              return ctx.refreshDashboard();\n            });\n            i0.ɵɵelementStart(6, \"mat-icon\");\n            i0.ɵɵtext(7, \"refresh\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(8, \" Refresh \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(9, DashboardComponent_div_9_Template, 4, 0, \"div\", 5)(10, DashboardComponent_div_10_Template, 7, 1, \"div\", 6)(11, DashboardComponent_div_11_Template, 132, 24, \"div\", 7);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 4, ctx.currentUser$));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.stats);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, i1.AsyncPipe, i1.DecimalPipe, MatCardModule, i2.MatCard, i2.MatCardActions, i2.MatCardAvatar, i2.MatCardContent, i2.MatCardHeader, i2.MatCardSubtitle, i2.MatCardTitle, MatButtonModule, i3.MatButton, MatIconModule, i4.MatIcon, MatProgressSpinnerModule, i5.MatProgressSpinner],\n        styles: [\".dashboard-container[_ngcontent-%COMP%]{padding:24px;background-color:#f5f5f5;min-height:100vh}.dashboard-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:32px}.dashboard-header[_ngcontent-%COMP%]   .welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0 0 8px;color:#333;font-weight:600;font-size:28px}.dashboard-header[_ngcontent-%COMP%]   .welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:16px}.dashboard-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:64px 24px;text-align:center}.loading-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:16px 0;color:#666;font-size:16px}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:24px;margin-bottom:32px}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{transition:transform .2s ease,box-shadow .2s ease}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 24px #0000001f}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-avatar[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;color:#fff}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-avatar.users-avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-avatar.products-avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c)}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-avatar.orders-avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#00f2fe)}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-avatar.revenue-avatar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#43e97b,#38f9d7)}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{font-size:32px;font-weight:700;color:#333;margin-bottom:16px}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-details[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:14px;color:#666}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-details[_ngcontent-%COMP%]   .stat-item.warning[_ngcontent-%COMP%]{color:#ff9800}.dashboard-content[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-details[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;margin-right:8px}.dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:24px;margin-bottom:32px}.dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{height:400px}.dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{height:calc(100% - 80px);display:flex;align-items:center;justify-content:center}.dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .chart-wrapper[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center}.dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]{color:#666;font-style:italic;text-align:center}.dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;color:#999}.dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;margin-bottom:16px}.dashboard-content[_ngcontent-%COMP%]   .charts-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]{max-height:400px;overflow-y:auto}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;padding:16px 0;border-bottom:1px solid #eee}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%]{margin-right:16px;margin-top:4px}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]{flex:1}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-description[_ngcontent-%COMP%]{margin:0 0 8px;color:#333;font-size:14px;line-height:1.4}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-meta[_ngcontent-%COMP%]{display:flex;gap:16px;font-size:12px;color:#999}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-meta[_ngcontent-%COMP%]   .activity-user[_ngcontent-%COMP%]{font-weight:500}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .no-activities[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:32px;color:#999}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .no-activities[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;margin-bottom:16px}.dashboard-content[_ngcontent-%COMP%]   .activities-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .no-activities[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px}@media (max-width: 768px){.dashboard-container[_ngcontent-%COMP%]{padding:16px}.dashboard-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:16px}.dashboard-header[_ngcontent-%COMP%]   .welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px}.stats-grid[_ngcontent-%COMP%], .charts-section[_ngcontent-%COMP%]{grid-template-columns:1fr}.charts-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{height:300px}}@media (max-width: 480px){.dashboard-container[_ngcontent-%COMP%]{padding:12px}.stats-grid[_ngcontent-%COMP%], .charts-section[_ngcontent-%COMP%]{gap:16px}}\"]\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}