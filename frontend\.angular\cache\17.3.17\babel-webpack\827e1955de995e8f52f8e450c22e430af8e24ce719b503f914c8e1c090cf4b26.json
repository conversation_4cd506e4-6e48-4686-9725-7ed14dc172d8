{"ast": null, "code": "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { getSupportedInputTypes } from '@angular/cdk/platform';\nimport * as i4 from '@angular/cdk/text-field';\nimport { TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Optional, Self, Inject, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport * as i3 from '@angular/material/core';\nimport { _ErrorStateTracker, MatCommonModule } from '@angular/material/core';\nimport * as i5 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nexport { Mat<PERSON>rror, MatFormField, MatHint, MatLabel, MatPrefix, MatSuffix } from '@angular/material/form-field';\nimport { Subject } from 'rxjs';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\nconst MAT_INPUT_VALUE_ACCESSOR = /*#__PURE__*/new InjectionToken('MAT_INPUT_VALUE_ACCESSOR');\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = ['button', 'checkbox', 'file', 'hidden', 'image', 'radio', 'range', 'reset', 'submit'];\nlet nextUniqueId = 0;\nlet MatInput = /*#__PURE__*/(() => {\n  class MatInput {\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      // Browsers may not fire the blur event if the input is disabled too quickly.\n      // Reset from here to ensure that the element doesn't become stuck.\n      if (this.focused) {\n        this.focused = false;\n        this.stateChanges.next();\n      }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n      return this._id;\n    }\n    set id(value) {\n      this._id = value || this._uid;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n      return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n      this._required = coerceBooleanProperty(value);\n    }\n    /** Input type of the element. */\n    get type() {\n      return this._type;\n    }\n    set type(value) {\n      this._type = value || 'text';\n      this._validateType();\n      // When using Angular inputs, developers are no longer able to set the properties on the native\n      // input element. To ensure that bindings for `type` work, we need to sync the setter\n      // with the native property. Textarea elements don't support the type property or attribute.\n      if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n        this._elementRef.nativeElement.type = this._type;\n      }\n    }\n    /** An object used to control when error messages are shown. */\n    get errorStateMatcher() {\n      return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n      this._errorStateTracker.matcher = value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n      return this._inputValueAccessor.value;\n    }\n    set value(value) {\n      if (value !== this.value) {\n        this._inputValueAccessor.value = value;\n        this.stateChanges.next();\n      }\n    }\n    /** Whether the element is readonly. */\n    get readonly() {\n      return this._readonly;\n    }\n    set readonly(value) {\n      this._readonly = coerceBooleanProperty(value);\n    }\n    /** Whether the input is in an error state. */\n    get errorState() {\n      return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n      this._errorStateTracker.errorState = value;\n    }\n    constructor(_elementRef, _platform, ngControl, parentForm, parentFormGroup, defaultErrorStateMatcher, inputValueAccessor, _autofillMonitor, ngZone,\n    // TODO: Remove this once the legacy appearance has been removed. We only need\n    // to inject the form field for determining whether the placeholder has been promoted.\n    _formField) {\n      this._elementRef = _elementRef;\n      this._platform = _platform;\n      this.ngControl = ngControl;\n      this._autofillMonitor = _autofillMonitor;\n      this._formField = _formField;\n      this._uid = `mat-input-${nextUniqueId++}`;\n      /**\n       * Implemented as part of MatFormFieldControl.\n       * @docs-private\n       */\n      this.focused = false;\n      /**\n       * Implemented as part of MatFormFieldControl.\n       * @docs-private\n       */\n      this.stateChanges = new Subject();\n      /**\n       * Implemented as part of MatFormFieldControl.\n       * @docs-private\n       */\n      this.controlType = 'mat-input';\n      /**\n       * Implemented as part of MatFormFieldControl.\n       * @docs-private\n       */\n      this.autofilled = false;\n      this._disabled = false;\n      this._type = 'text';\n      this._readonly = false;\n      this._neverEmptyInputTypes = ['date', 'datetime', 'datetime-local', 'month', 'time', 'week'].filter(t => getSupportedInputTypes().has(t));\n      this._iOSKeyupListener = event => {\n        const el = event.target;\n        // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n        // indicate different things. If the value is 0, it means that the caret is at the start\n        // of the input, whereas a value of `null` means that the input doesn't support\n        // manipulating the selection range. Inputs that don't support setting the selection range\n        // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n        // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n        if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n          // Note: Just setting `0, 0` doesn't fix the issue. Setting\n          // `1, 1` fixes it for the first time that you type text and\n          // then hold delete. Toggling to `1, 1` and then back to\n          // `0, 0` seems to completely fix it.\n          el.setSelectionRange(1, 1);\n          el.setSelectionRange(0, 0);\n        }\n      };\n      const element = this._elementRef.nativeElement;\n      const nodeName = element.nodeName.toLowerCase();\n      // If no input value accessor was explicitly specified, use the element as the input value\n      // accessor.\n      this._inputValueAccessor = inputValueAccessor || element;\n      this._previousNativeValue = this.value;\n      // Force setter to be called in case id was not specified.\n      this.id = this.id;\n      // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n      // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n      // exists on iOS, we only bother to install the listener on iOS.\n      if (_platform.IOS) {\n        ngZone.runOutsideAngular(() => {\n          _elementRef.nativeElement.addEventListener('keyup', this._iOSKeyupListener);\n        });\n      }\n      this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, ngControl, parentFormGroup, parentForm, this.stateChanges);\n      this._isServer = !this._platform.isBrowser;\n      this._isNativeSelect = nodeName === 'select';\n      this._isTextarea = nodeName === 'textarea';\n      this._isInFormField = !!_formField;\n      if (this._isNativeSelect) {\n        this.controlType = element.multiple ? 'mat-native-select-multiple' : 'mat-native-select';\n      }\n    }\n    ngAfterViewInit() {\n      if (this._platform.isBrowser) {\n        this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n          this.autofilled = event.isAutofilled;\n          this.stateChanges.next();\n        });\n      }\n    }\n    ngOnChanges() {\n      this.stateChanges.next();\n    }\n    ngOnDestroy() {\n      this.stateChanges.complete();\n      if (this._platform.isBrowser) {\n        this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n      }\n      if (this._platform.IOS) {\n        this._elementRef.nativeElement.removeEventListener('keyup', this._iOSKeyupListener);\n      }\n    }\n    ngDoCheck() {\n      if (this.ngControl) {\n        // We need to re-evaluate this on every change detection cycle, because there are some\n        // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n        // that whatever logic is in here has to be super lean or we risk destroying the performance.\n        this.updateErrorState();\n        // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n        // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n        // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n        // disabled.\n        if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n          this.disabled = this.ngControl.disabled;\n          this.stateChanges.next();\n        }\n      }\n      // We need to dirty-check the native element's value, because there are some cases where\n      // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n      // updating the value using `emitEvent: false`).\n      this._dirtyCheckNativeValue();\n      // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n      // present or not depends on a query which is prone to \"changed after checked\" errors.\n      this._dirtyCheckPlaceholder();\n    }\n    /** Focuses the input. */\n    focus(options) {\n      this._elementRef.nativeElement.focus(options);\n    }\n    /** Refreshes the error state of the input. */\n    updateErrorState() {\n      this._errorStateTracker.updateErrorState();\n    }\n    /** Callback for the cases where the focused state of the input changes. */\n    _focusChanged(isFocused) {\n      if (isFocused !== this.focused) {\n        this.focused = isFocused;\n        this.stateChanges.next();\n      }\n    }\n    _onInput() {\n      // This is a noop function and is used to let Angular know whenever the value changes.\n      // Angular will run a new change detection each time the `input` event has been dispatched.\n      // It's necessary that Angular recognizes the value change, because when floatingLabel\n      // is set to false and Angular forms aren't used, the placeholder won't recognize the\n      // value changes and will not disappear.\n      // Listening to the input event wouldn't be necessary when the input is using the\n      // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n    }\n    /** Does some manual dirty checking on the native input `value` property. */\n    _dirtyCheckNativeValue() {\n      const newValue = this._elementRef.nativeElement.value;\n      if (this._previousNativeValue !== newValue) {\n        this._previousNativeValue = newValue;\n        this.stateChanges.next();\n      }\n    }\n    /** Does some manual dirty checking on the native input `placeholder` attribute. */\n    _dirtyCheckPlaceholder() {\n      const placeholder = this._getPlaceholder();\n      if (placeholder !== this._previousPlaceholder) {\n        const element = this._elementRef.nativeElement;\n        this._previousPlaceholder = placeholder;\n        placeholder ? element.setAttribute('placeholder', placeholder) : element.removeAttribute('placeholder');\n      }\n    }\n    /** Gets the current placeholder of the form field. */\n    _getPlaceholder() {\n      return this.placeholder || null;\n    }\n    /** Make sure the input is a supported type. */\n    _validateType() {\n      if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatInputUnsupportedTypeError(this._type);\n      }\n    }\n    /** Checks whether the input type is one of the types that are never empty. */\n    _isNeverEmpty() {\n      return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n    }\n    /** Checks whether the input is invalid based on the native validation. */\n    _isBadInput() {\n      // The `validity` property won't be present on platform-server.\n      let validity = this._elementRef.nativeElement.validity;\n      return validity && validity.badInput;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n      return !this._isNeverEmpty() && !this._elementRef.nativeElement.value && !this._isBadInput() && !this.autofilled;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n      if (this._isNativeSelect) {\n        // For a single-selection `<select>`, the label should float when the selected option has\n        // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n        // overlapping the label with the options.\n        const selectElement = this._elementRef.nativeElement;\n        const firstOption = selectElement.options[0];\n        // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n        // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n        return this.focused || selectElement.multiple || !this.empty || !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label);\n      } else {\n        return this.focused || !this.empty;\n      }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n      if (ids.length) {\n        this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n      } else {\n        this._elementRef.nativeElement.removeAttribute('aria-describedby');\n      }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n      // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n      // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n      // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n      if (!this.focused) {\n        this.focus();\n      }\n    }\n    /** Whether the form control is a native select that is displayed inline. */\n    _isInlineSelect() {\n      const element = this._elementRef.nativeElement;\n      return this._isNativeSelect && (element.multiple || element.size > 1);\n    }\n    static {\n      this.ɵfac = function MatInput_Factory(t) {\n        return new (t || MatInput)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i2.NgControl, 10), i0.ɵɵdirectiveInject(i2.NgForm, 8), i0.ɵɵdirectiveInject(i2.FormGroupDirective, 8), i0.ɵɵdirectiveInject(i3.ErrorStateMatcher), i0.ɵɵdirectiveInject(MAT_INPUT_VALUE_ACCESSOR, 10), i0.ɵɵdirectiveInject(i4.AutofillMonitor), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatInput,\n        selectors: [[\"input\", \"matInput\", \"\"], [\"textarea\", \"matInput\", \"\"], [\"select\", \"matNativeControl\", \"\"], [\"input\", \"matNativeControl\", \"\"], [\"textarea\", \"matNativeControl\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-input-element\"],\n        hostVars: 18,\n        hostBindings: function MatInput_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"focus\", function MatInput_focus_HostBindingHandler() {\n              return ctx._focusChanged(true);\n            })(\"blur\", function MatInput_blur_HostBindingHandler() {\n              return ctx._focusChanged(false);\n            })(\"input\", function MatInput_input_HostBindingHandler() {\n              return ctx._onInput();\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"id\", ctx.id)(\"disabled\", ctx.disabled)(\"required\", ctx.required);\n            i0.ɵɵattribute(\"name\", ctx.name || null)(\"readonly\", ctx.readonly && !ctx._isNativeSelect || null)(\"aria-invalid\", ctx.empty && ctx.required ? null : ctx.errorState)(\"aria-required\", ctx.required)(\"id\", ctx.id);\n            i0.ɵɵclassProp(\"mat-input-server\", ctx._isServer)(\"mat-mdc-form-field-textarea-control\", ctx._isInFormField && ctx._isTextarea)(\"mat-mdc-form-field-input-control\", ctx._isInFormField)(\"mdc-text-field__input\", ctx._isInFormField)(\"mat-mdc-native-select-inline\", ctx._isInlineSelect());\n          }\n        },\n        inputs: {\n          disabled: \"disabled\",\n          id: \"id\",\n          placeholder: \"placeholder\",\n          name: \"name\",\n          required: \"required\",\n          type: \"type\",\n          errorStateMatcher: \"errorStateMatcher\",\n          userAriaDescribedBy: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"userAriaDescribedBy\"],\n          value: \"value\",\n          readonly: \"readonly\"\n        },\n        exportAs: [\"matInput\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: MatFormFieldControl,\n          useExisting: MatInput\n        }]), i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return MatInput;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatInputModule = /*#__PURE__*/(() => {\n  class MatInputModule {\n    static {\n      this.ɵfac = function MatInputModule_Factory(t) {\n        return new (t || MatInputModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatInputModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule]\n      });\n    }\n  }\n  return MatInputModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n//# sourceMappingURL=input.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}