{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber, OperatorSubscriber } from './OperatorSubscriber';\nexport function groupBy(keySelector, elementOrOptions, duration, connector) {\n  return operate((source, subscriber) => {\n    let element;\n    if (!elementOrOptions || typeof elementOrOptions === 'function') {\n      element = elementOrOptions;\n    } else {\n      ({\n        duration,\n        element,\n        connector\n      } = elementOrOptions);\n    }\n    const groups = new Map();\n    const notify = cb => {\n      groups.forEach(cb);\n      cb(subscriber);\n    };\n    const handleError = err => notify(consumer => consumer.error(err));\n    let activeGroups = 0;\n    let teardownAttempted = false;\n    const groupBySourceSubscriber = new OperatorSubscriber(subscriber, value => {\n      try {\n        const key = keySelector(value);\n        let group = groups.get(key);\n        if (!group) {\n          groups.set(key, group = connector ? connector() : new Subject());\n          const grouped = createGroupedObservable(key, group);\n          subscriber.next(grouped);\n          if (duration) {\n            const durationSubscriber = createOperatorSubscriber(group, () => {\n              group.complete();\n              durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            }, undefined, undefined, () => groups.delete(key));\n            groupBySourceSubscriber.add(innerFrom(duration(grouped)).subscribe(durationSubscriber));\n          }\n        }\n        group.next(element ? element(value) : value);\n      } catch (err) {\n        handleError(err);\n      }\n    }, () => notify(consumer => consumer.complete()), handleError, () => groups.clear(), () => {\n      teardownAttempted = true;\n      return activeGroups === 0;\n    });\n    source.subscribe(groupBySourceSubscriber);\n    function createGroupedObservable(key, groupSubject) {\n      const result = new Observable(groupSubscriber => {\n        activeGroups++;\n        const innerSub = groupSubject.subscribe(groupSubscriber);\n        return () => {\n          innerSub.unsubscribe();\n          --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n        };\n      });\n      result.key = key;\n      return result;\n    }\n  });\n}\n//# sourceMappingURL=groupBy.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}